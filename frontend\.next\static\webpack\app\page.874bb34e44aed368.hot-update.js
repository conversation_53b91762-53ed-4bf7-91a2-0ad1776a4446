"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/error-utils.ts":
/*!****************************!*\
  !*** ./lib/error-utils.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AUTH_ERROR_CODES: () => (/* binding */ AUTH_ERROR_CODES),\n/* harmony export */   getAuthErrorMessage: () => (/* binding */ getAuthErrorMessage),\n/* harmony export */   getErrorUIState: () => (/* binding */ getErrorUIState),\n/* harmony export */   handleAuthError: () => (/* binding */ handleAuthError),\n/* harmony export */   isAuthError: () => (/* binding */ isAuthError),\n/* harmony export */   isTokenExpiredError: () => (/* binding */ isTokenExpiredError)\n/* harmony export */ });\n/**\n * 前端错误处理工具函数\n * 配合后端的结构化错误响应，提供统一的错误识别和处理\n * 包含前端敏感信息过滤功能，作为最后一道防线\n */ // 认证相关错误码\nconst AUTH_ERROR_CODES = {\n    TOKEN_EXPIRED: 'TOKEN_EXPIRED',\n    TOKEN_INVALID: 'TOKEN_INVALID',\n    TOKEN_TYPE_INVALID: 'TOKEN_TYPE_INVALID',\n    NO_TOKEN: 'NO_TOKEN',\n    AUTH_ERROR: 'AUTH_ERROR',\n    REFRESH_TOKEN_EXPIRED: 'REFRESH_TOKEN_EXPIRED'\n};\n/**\n * 判断是否为认证相关错误\n * @param error 错误对象\n * @returns 是否为认证错误\n */ function isAuthError(error) {\n    // 优先检查错误码（最可靠）\n    if (error === null || error === void 0 ? void 0 : error.code) {\n        return Object.values(AUTH_ERROR_CODES).includes(error.code);\n    }\n    // 兼容性检查：检查错误消息\n    if (error === null || error === void 0 ? void 0 : error.message) {\n        const message = error.message.toLowerCase();\n        return message.includes('token') || message.includes('expired') || message.includes('unauthorized') || message.includes('401') || message.includes('登录') || message.includes('refresh');\n    }\n    return false;\n}\n/**\n * 判断是否为token过期错误\n * @param error 错误对象\n * @returns 是否为token过期错误\n */ function isTokenExpiredError(error) {\n    // 优先检查错误码\n    if ((error === null || error === void 0 ? void 0 : error.code) === AUTH_ERROR_CODES.TOKEN_EXPIRED) {\n        return true;\n    }\n    // 兼容性检查：检查错误消息\n    if (error === null || error === void 0 ? void 0 : error.message) {\n        const message = error.message.toLowerCase();\n        return message.includes('token expired') || message.includes('expired') || message.includes('过期');\n    }\n    return false;\n}\n/**\n * 获取用户友好的错误消息\n * @param error 错误对象\n * @returns 用户友好的错误消息\n */ function getAuthErrorMessage(error) {\n    if (isAuthError(error)) {\n        // 【修复】检查错误对象上的shouldRedirect属性，默认为true\n        const shouldRedirect = (error === null || error === void 0 ? void 0 : error.shouldRedirect) !== undefined ? error.shouldRedirect : true;\n        return {\n            title: \"认证失败\",\n            description: \"会话已过期，正在跳转到登录页面...\",\n            shouldRedirect: shouldRedirect\n        };\n    }\n    // 业务错误或网络错误\n    return {\n        title: \"操作失败\",\n        description: (error === null || error === void 0 ? void 0 : error.message) || \"请检查网络连接后重试\",\n        shouldRedirect: false\n    };\n}\n/**\n * 统一的认证错误处理函数\n * @param error 错误对象\n * @param onAuthError 认证错误回调（可选）\n * @returns 处理结果\n */ function handleAuthError(error, onAuthError) {\n    const isAuth = isAuthError(error);\n    if (isAuth && onAuthError) {\n        onAuthError();\n    }\n    const errorInfo = getAuthErrorMessage(error);\n    return {\n        isAuthError: isAuth,\n        message: errorInfo.description,\n        shouldRedirect: errorInfo.shouldRedirect\n    };\n}\n/**\n * 为页面组件提供的错误处理hook辅助函数\n * @param error 错误对象\n * @returns UI状态更新信息\n */ function getErrorUIState(error) {\n    var _error_message;\n    if (isAuthError(error)) {\n        return {\n            statusDisplay: \"请重新登录\",\n            toastTitle: \"认证失败\",\n            toastDescription: \"会话已过期，正在跳转到登录页面...\",\n            variant: 'destructive'\n        };\n    }\n    // 检查是否为业务错误（如卡密无效等）\n    if (error === null || error === void 0 ? void 0 : (_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('卡密')) {\n        return {\n            statusDisplay: \"充值失败\",\n            toastTitle: \"充值失败\",\n            toastDescription: \"卡密无效或已使用，请检查后重试\",\n            variant: 'destructive'\n        };\n    }\n    // 网络或其他错误\n    return {\n        statusDisplay: \"获取失败\",\n        toastTitle: \"操作失败\",\n        toastDescription: (error === null || error === void 0 ? void 0 : error.message) || \"请检查网络连接后重试\",\n        variant: 'destructive'\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2xpYi9lcnJvci11dGlscy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBQTs7OztDQUlDLEdBRUQsVUFBVTtBQUNILE1BQU1BLG1CQUFtQjtJQUM5QkMsZUFBZTtJQUNmQyxlQUFlO0lBQ2ZDLG9CQUFvQjtJQUNwQkMsVUFBVTtJQUNWQyxZQUFZO0lBQ1pDLHVCQUF1QjtBQUN6QixFQUFVO0FBVVY7Ozs7Q0FJQyxHQUNNLFNBQVNDLFlBQVlDLEtBQVU7SUFDcEMsZUFBZTtJQUNmLElBQUlBLGtCQUFBQSw0QkFBQUEsTUFBT0MsSUFBSSxFQUFFO1FBQ2YsT0FBT0MsT0FBT0MsTUFBTSxDQUFDWCxrQkFBa0JZLFFBQVEsQ0FBQ0osTUFBTUMsSUFBSTtJQUM1RDtJQUVBLGVBQWU7SUFDZixJQUFJRCxrQkFBQUEsNEJBQUFBLE1BQU9LLE9BQU8sRUFBRTtRQUNsQixNQUFNQSxVQUFVTCxNQUFNSyxPQUFPLENBQUNDLFdBQVc7UUFDekMsT0FBT0QsUUFBUUQsUUFBUSxDQUFDLFlBQ2pCQyxRQUFRRCxRQUFRLENBQUMsY0FDakJDLFFBQVFELFFBQVEsQ0FBQyxtQkFDakJDLFFBQVFELFFBQVEsQ0FBQyxVQUNqQkMsUUFBUUQsUUFBUSxDQUFDLFNBQ2pCQyxRQUFRRCxRQUFRLENBQUM7SUFDMUI7SUFFQSxPQUFPO0FBQ1Q7QUFFQTs7OztDQUlDLEdBQ00sU0FBU0csb0JBQW9CUCxLQUFVO0lBQzVDLFVBQVU7SUFDVixJQUFJQSxDQUFBQSxrQkFBQUEsNEJBQUFBLE1BQU9DLElBQUksTUFBS1QsaUJBQWlCQyxhQUFhLEVBQUU7UUFDbEQsT0FBTztJQUNUO0lBRUEsZUFBZTtJQUNmLElBQUlPLGtCQUFBQSw0QkFBQUEsTUFBT0ssT0FBTyxFQUFFO1FBQ2xCLE1BQU1BLFVBQVVMLE1BQU1LLE9BQU8sQ0FBQ0MsV0FBVztRQUN6QyxPQUFPRCxRQUFRRCxRQUFRLENBQUMsb0JBQ2pCQyxRQUFRRCxRQUFRLENBQUMsY0FDakJDLFFBQVFELFFBQVEsQ0FBQztJQUMxQjtJQUVBLE9BQU87QUFDVDtBQUVBOzs7O0NBSUMsR0FDTSxTQUFTSSxvQkFBb0JSLEtBQVU7SUFLNUMsSUFBSUQsWUFBWUMsUUFBUTtRQUN0Qix1Q0FBdUM7UUFDdkMsTUFBTVMsaUJBQWlCVCxDQUFBQSxrQkFBQUEsNEJBQUFBLE1BQU9TLGNBQWMsTUFBS0MsWUFBWVYsTUFBTVMsY0FBYyxHQUFHO1FBRXBGLE9BQU87WUFDTEUsT0FBTztZQUNQQyxhQUFhO1lBQ2JILGdCQUFnQkE7UUFDbEI7SUFDRjtJQUVBLFlBQVk7SUFDWixPQUFPO1FBQ0xFLE9BQU87UUFDUEMsYUFBYVosQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPSyxPQUFPLEtBQUk7UUFDL0JJLGdCQUFnQjtJQUNsQjtBQUNGO0FBRUE7Ozs7O0NBS0MsR0FDTSxTQUFTSSxnQkFDZGIsS0FBVSxFQUNWYyxXQUF3QjtJQU14QixNQUFNQyxTQUFTaEIsWUFBWUM7SUFFM0IsSUFBSWUsVUFBVUQsYUFBYTtRQUN6QkE7SUFDRjtJQUVBLE1BQU1FLFlBQVlSLG9CQUFvQlI7SUFFdEMsT0FBTztRQUNMRCxhQUFhZ0I7UUFDYlYsU0FBU1csVUFBVUosV0FBVztRQUM5QkgsZ0JBQWdCTyxVQUFVUCxjQUFjO0lBQzFDO0FBQ0Y7QUFFQTs7OztDQUlDLEdBQ00sU0FBU1EsZ0JBQWdCakIsS0FBVTtRQWdCcENBO0lBVkosSUFBSUQsWUFBWUMsUUFBUTtRQUN0QixPQUFPO1lBQ0xrQixlQUFlO1lBQ2ZDLFlBQVk7WUFDWkMsa0JBQWtCO1lBQ2xCQyxTQUFTO1FBQ1g7SUFDRjtJQUVBLG9CQUFvQjtJQUNwQixJQUFJckIsa0JBQUFBLDZCQUFBQSxpQkFBQUEsTUFBT0ssT0FBTyxjQUFkTCxxQ0FBQUEsZUFBZ0JJLFFBQVEsQ0FBQyxPQUFPO1FBQ2xDLE9BQU87WUFDTGMsZUFBZTtZQUNmQyxZQUFZO1lBQ1pDLGtCQUFrQjtZQUNsQkMsU0FBUztRQUNYO0lBQ0Y7SUFFQSxVQUFVO0lBQ1YsT0FBTztRQUNMSCxlQUFlO1FBQ2ZDLFlBQVk7UUFDWkMsa0JBQWtCcEIsQ0FBQUEsa0JBQUFBLDRCQUFBQSxNQUFPSyxPQUFPLEtBQUk7UUFDcENnQixTQUFTO0lBQ1g7QUFDRiIsInNvdXJjZXMiOlsiRDpcXG15YWl0dHNcXGZyb250ZW5kXFxsaWJcXGVycm9yLXV0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICog5YmN56uv6ZSZ6K+v5aSE55CG5bel5YW35Ye95pWwXG4gKiDphY3lkIjlkI7nq6/nmoTnu5PmnoTljJbplJnor6/lk43lupTvvIzmj5Dkvpvnu5/kuIDnmoTplJnor6/or4bliKvlkozlpITnkIZcbiAqIOWMheWQq+WJjeerr+aVj+aEn+S/oeaBr+i/h+a7pOWKn+iDve+8jOS9nOS4uuacgOWQjuS4gOmBk+mYsue6v1xuICovXG5cbi8vIOiupOivgeebuOWFs+mUmeivr+eggVxuZXhwb3J0IGNvbnN0IEFVVEhfRVJST1JfQ09ERVMgPSB7XG4gIFRPS0VOX0VYUElSRUQ6ICdUT0tFTl9FWFBJUkVEJyxcbiAgVE9LRU5fSU5WQUxJRDogJ1RPS0VOX0lOVkFMSUQnLFxuICBUT0tFTl9UWVBFX0lOVkFMSUQ6ICdUT0tFTl9UWVBFX0lOVkFMSUQnLFxuICBOT19UT0tFTjogJ05PX1RPS0VOJyxcbiAgQVVUSF9FUlJPUjogJ0FVVEhfRVJST1InLFxuICBSRUZSRVNIX1RPS0VOX0VYUElSRUQ6ICdSRUZSRVNIX1RPS0VOX0VYUElSRUQnXG59IGFzIGNvbnN0XG5cbi8vIOmUmeivr+exu+Wei1xuZXhwb3J0IHR5cGUgQXV0aEVycm9yQ29kZSA9IHR5cGVvZiBBVVRIX0VSUk9SX0NPREVTW2tleW9mIHR5cGVvZiBBVVRIX0VSUk9SX0NPREVTXVxuXG4vLyDmianlsZXnmoTplJnor6/lr7nosaHmjqXlj6NcbmV4cG9ydCBpbnRlcmZhY2UgRXh0ZW5kZWRFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgY29kZT86IHN0cmluZ1xufVxuXG4vKipcbiAqIOWIpOaWreaYr+WQpuS4uuiupOivgeebuOWFs+mUmeivr1xuICogQHBhcmFtIGVycm9yIOmUmeivr+WvueixoVxuICogQHJldHVybnMg5piv5ZCm5Li66K6k6K+B6ZSZ6K+vXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpc0F1dGhFcnJvcihlcnJvcjogYW55KTogYm9vbGVhbiB7XG4gIC8vIOS8mOWFiOajgOafpemUmeivr+egge+8iOacgOWPr+mdoO+8iVxuICBpZiAoZXJyb3I/LmNvZGUpIHtcbiAgICByZXR1cm4gT2JqZWN0LnZhbHVlcyhBVVRIX0VSUk9SX0NPREVTKS5pbmNsdWRlcyhlcnJvci5jb2RlKVxuICB9XG4gIFxuICAvLyDlhbzlrrnmgKfmo4Dmn6XvvJrmo4Dmn6XplJnor6/mtojmga9cbiAgaWYgKGVycm9yPy5tZXNzYWdlKSB7XG4gICAgY29uc3QgbWVzc2FnZSA9IGVycm9yLm1lc3NhZ2UudG9Mb3dlckNhc2UoKVxuICAgIHJldHVybiBtZXNzYWdlLmluY2x1ZGVzKCd0b2tlbicpIHx8XG4gICAgICAgICAgIG1lc3NhZ2UuaW5jbHVkZXMoJ2V4cGlyZWQnKSB8fFxuICAgICAgICAgICBtZXNzYWdlLmluY2x1ZGVzKCd1bmF1dGhvcml6ZWQnKSB8fFxuICAgICAgICAgICBtZXNzYWdlLmluY2x1ZGVzKCc0MDEnKSB8fFxuICAgICAgICAgICBtZXNzYWdlLmluY2x1ZGVzKCfnmbvlvZUnKSB8fFxuICAgICAgICAgICBtZXNzYWdlLmluY2x1ZGVzKCdyZWZyZXNoJylcbiAgfVxuICBcbiAgcmV0dXJuIGZhbHNlXG59XG5cbi8qKlxuICog5Yik5pat5piv5ZCm5Li6dG9rZW7ov4fmnJ/plJnor69cbiAqIEBwYXJhbSBlcnJvciDplJnor6/lr7nosaFcbiAqIEByZXR1cm5zIOaYr+WQpuS4unRva2Vu6L+H5pyf6ZSZ6K+vXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpc1Rva2VuRXhwaXJlZEVycm9yKGVycm9yOiBhbnkpOiBib29sZWFuIHtcbiAgLy8g5LyY5YWI5qOA5p+l6ZSZ6K+v56CBXG4gIGlmIChlcnJvcj8uY29kZSA9PT0gQVVUSF9FUlJPUl9DT0RFUy5UT0tFTl9FWFBJUkVEKSB7XG4gICAgcmV0dXJuIHRydWVcbiAgfVxuICBcbiAgLy8g5YW85a655oCn5qOA5p+l77ya5qOA5p+l6ZSZ6K+v5raI5oGvXG4gIGlmIChlcnJvcj8ubWVzc2FnZSkge1xuICAgIGNvbnN0IG1lc3NhZ2UgPSBlcnJvci5tZXNzYWdlLnRvTG93ZXJDYXNlKClcbiAgICByZXR1cm4gbWVzc2FnZS5pbmNsdWRlcygndG9rZW4gZXhwaXJlZCcpIHx8XG4gICAgICAgICAgIG1lc3NhZ2UuaW5jbHVkZXMoJ2V4cGlyZWQnKSB8fFxuICAgICAgICAgICBtZXNzYWdlLmluY2x1ZGVzKCfov4fmnJ8nKVxuICB9XG4gIFxuICByZXR1cm4gZmFsc2Vcbn1cblxuLyoqXG4gKiDojrflj5bnlKjmiLflj4vlpb3nmoTplJnor6/mtojmga9cbiAqIEBwYXJhbSBlcnJvciDplJnor6/lr7nosaFcbiAqIEByZXR1cm5zIOeUqOaIt+WPi+WlveeahOmUmeivr+a2iOaBr1xuICovXG5leHBvcnQgZnVuY3Rpb24gZ2V0QXV0aEVycm9yTWVzc2FnZShlcnJvcjogYW55KToge1xuICB0aXRsZTogc3RyaW5nXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmdcbiAgc2hvdWxkUmVkaXJlY3Q6IGJvb2xlYW5cbn0ge1xuICBpZiAoaXNBdXRoRXJyb3IoZXJyb3IpKSB7XG4gICAgLy8g44CQ5L+u5aSN44CR5qOA5p+l6ZSZ6K+v5a+56LGh5LiK55qEc2hvdWxkUmVkaXJlY3TlsZ7mgKfvvIzpu5jorqTkuLp0cnVlXG4gICAgY29uc3Qgc2hvdWxkUmVkaXJlY3QgPSBlcnJvcj8uc2hvdWxkUmVkaXJlY3QgIT09IHVuZGVmaW5lZCA/IGVycm9yLnNob3VsZFJlZGlyZWN0IDogdHJ1ZVxuXG4gICAgcmV0dXJuIHtcbiAgICAgIHRpdGxlOiBcIuiupOivgeWksei0pVwiLFxuICAgICAgZGVzY3JpcHRpb246IFwi5Lya6K+d5bey6L+H5pyf77yM5q2j5Zyo6Lez6L2s5Yiw55m75b2V6aG16Z2iLi4uXCIsXG4gICAgICBzaG91bGRSZWRpcmVjdDogc2hvdWxkUmVkaXJlY3RcbiAgICB9XG4gIH1cblxuICAvLyDkuJrliqHplJnor6/miJbnvZHnu5zplJnor69cbiAgcmV0dXJuIHtcbiAgICB0aXRsZTogXCLmk43kvZzlpLHotKVcIixcbiAgICBkZXNjcmlwdGlvbjogZXJyb3I/Lm1lc3NhZ2UgfHwgXCLor7fmo4Dmn6XnvZHnu5zov57mjqXlkI7ph43or5VcIixcbiAgICBzaG91bGRSZWRpcmVjdDogZmFsc2VcbiAgfVxufVxuXG4vKipcbiAqIOe7n+S4gOeahOiupOivgemUmeivr+WkhOeQhuWHveaVsFxuICogQHBhcmFtIGVycm9yIOmUmeivr+WvueixoVxuICogQHBhcmFtIG9uQXV0aEVycm9yIOiupOivgemUmeivr+Wbnuiwg++8iOWPr+mAie+8iVxuICogQHJldHVybnMg5aSE55CG57uT5p6cXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBoYW5kbGVBdXRoRXJyb3IoXG4gIGVycm9yOiBhbnksXG4gIG9uQXV0aEVycm9yPzogKCkgPT4gdm9pZFxuKToge1xuICBpc0F1dGhFcnJvcjogYm9vbGVhblxuICBtZXNzYWdlOiBzdHJpbmdcbiAgc2hvdWxkUmVkaXJlY3Q6IGJvb2xlYW5cbn0ge1xuICBjb25zdCBpc0F1dGggPSBpc0F1dGhFcnJvcihlcnJvcilcbiAgXG4gIGlmIChpc0F1dGggJiYgb25BdXRoRXJyb3IpIHtcbiAgICBvbkF1dGhFcnJvcigpXG4gIH1cbiAgXG4gIGNvbnN0IGVycm9ySW5mbyA9IGdldEF1dGhFcnJvck1lc3NhZ2UoZXJyb3IpXG4gIFxuICByZXR1cm4ge1xuICAgIGlzQXV0aEVycm9yOiBpc0F1dGgsXG4gICAgbWVzc2FnZTogZXJyb3JJbmZvLmRlc2NyaXB0aW9uLFxuICAgIHNob3VsZFJlZGlyZWN0OiBlcnJvckluZm8uc2hvdWxkUmVkaXJlY3RcbiAgfVxufVxuXG4vKipcbiAqIOS4uumhtemdoue7hOS7tuaPkOS+m+eahOmUmeivr+WkhOeQhmhvb2vovoXliqnlh73mlbBcbiAqIEBwYXJhbSBlcnJvciDplJnor6/lr7nosaFcbiAqIEByZXR1cm5zIFVJ54q25oCB5pu05paw5L+h5oGvXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBnZXRFcnJvclVJU3RhdGUoZXJyb3I6IGFueSk6IHtcbiAgc3RhdHVzRGlzcGxheTogc3RyaW5nXG4gIHRvYXN0VGl0bGU6IHN0cmluZ1xuICB0b2FzdERlc2NyaXB0aW9uOiBzdHJpbmdcbiAgdmFyaWFudDogJ2RlZmF1bHQnIHwgJ2Rlc3RydWN0aXZlJ1xufSB7XG4gIGlmIChpc0F1dGhFcnJvcihlcnJvcikpIHtcbiAgICByZXR1cm4ge1xuICAgICAgc3RhdHVzRGlzcGxheTogXCLor7fph43mlrDnmbvlvZVcIixcbiAgICAgIHRvYXN0VGl0bGU6IFwi6K6k6K+B5aSx6LSlXCIsXG4gICAgICB0b2FzdERlc2NyaXB0aW9uOiBcIuS8muivneW3sui/h+acn++8jOato+WcqOi3s+i9rOWIsOeZu+W9lemhtemdoi4uLlwiLFxuICAgICAgdmFyaWFudDogJ2Rlc3RydWN0aXZlJ1xuICAgIH1cbiAgfVxuICBcbiAgLy8g5qOA5p+l5piv5ZCm5Li65Lia5Yqh6ZSZ6K+v77yI5aaC5Y2h5a+G5peg5pWI562J77yJXG4gIGlmIChlcnJvcj8ubWVzc2FnZT8uaW5jbHVkZXMoJ+WNoeWvhicpKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgIHN0YXR1c0Rpc3BsYXk6IFwi5YWF5YC85aSx6LSlXCIsXG4gICAgICB0b2FzdFRpdGxlOiBcIuWFheWAvOWksei0pVwiLCBcbiAgICAgIHRvYXN0RGVzY3JpcHRpb246IFwi5Y2h5a+G5peg5pWI5oiW5bey5L2/55So77yM6K+35qOA5p+l5ZCO6YeN6K+VXCIsXG4gICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnXG4gICAgfVxuICB9XG4gIFxuICAvLyDnvZHnu5zmiJblhbbku5bplJnor69cbiAgcmV0dXJuIHtcbiAgICBzdGF0dXNEaXNwbGF5OiBcIuiOt+WPluWksei0pVwiLFxuICAgIHRvYXN0VGl0bGU6IFwi5pON5L2c5aSx6LSlXCIsXG4gICAgdG9hc3REZXNjcmlwdGlvbjogZXJyb3I/Lm1lc3NhZ2UgfHwgXCLor7fmo4Dmn6XnvZHnu5zov57mjqXlkI7ph43or5VcIixcbiAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnXG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJBVVRIX0VSUk9SX0NPREVTIiwiVE9LRU5fRVhQSVJFRCIsIlRPS0VOX0lOVkFMSUQiLCJUT0tFTl9UWVBFX0lOVkFMSUQiLCJOT19UT0tFTiIsIkFVVEhfRVJST1IiLCJSRUZSRVNIX1RPS0VOX0VYUElSRUQiLCJpc0F1dGhFcnJvciIsImVycm9yIiwiY29kZSIsIk9iamVjdCIsInZhbHVlcyIsImluY2x1ZGVzIiwibWVzc2FnZSIsInRvTG93ZXJDYXNlIiwiaXNUb2tlbkV4cGlyZWRFcnJvciIsImdldEF1dGhFcnJvck1lc3NhZ2UiLCJzaG91bGRSZWRpcmVjdCIsInVuZGVmaW5lZCIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJoYW5kbGVBdXRoRXJyb3IiLCJvbkF1dGhFcnJvciIsImlzQXV0aCIsImVycm9ySW5mbyIsImdldEVycm9yVUlTdGF0ZSIsInN0YXR1c0Rpc3BsYXkiLCJ0b2FzdFRpdGxlIiwidG9hc3REZXNjcmlwdGlvbiIsInZhcmlhbnQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/error-utils.ts\n"));

/***/ })

});