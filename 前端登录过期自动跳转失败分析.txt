您遇到的“401错误发生但无弹窗、无跳转”的现象，根源在于 auth-service.ts 文件中的 withTokenRefresh 函数。这个函数设计了自动刷新Token的逻辑，但在一个特定的“双重失败”场景下，其错误处理链发生了中断，导致 page.tsx 无法捕获到它预期的认证错误。

结论先行

问题的核心在于 “双重认证失败” 场景：

第一次失败：您发送 getUserQuota 请求时，携带的 access_token 已过期，后端返回 401。

第二次失败：withTokenRefresh 逻辑被触发，它尝试使用 refresh_token 去获取新的 access_token。但您的 refresh_token 也已过期或失效，导致刷新请求再次失败（通常也是返回401或400）。

正是这个第二次失败的处理方式，虽然代码看起来很健壮，但最终导致了上层UI（page.tsx）的错误处理逻辑没有被正确触发。

详细分析：代码中的“双重失败”处理流程

让我们一步步跟踪代码的执行路径，看看问题究竟出在哪里。

第1步: 初始请求与第一次失败

在 page.tsx 中，代码调用 auth.getUserQuota()。我们来看 auth-service.ts 中对应的实现：

Generated typescript
// auth-service.ts
static async getUserQuota(): Promise<UserQuotaResponse> {
  try {
    // 关键调用：进入 withTokenRefresh 包装器
    return await this.withTokenRefresh(async () => {
      return await apiClient.get<UserQuotaResponse>(
        API_ENDPOINTS.USER.QUOTA,
        true // 需要认证
      )
    })
  } catch (error) {
    console.error('Get user quota error:', error)
    throw error // 这一层会重新抛出错误
  }
}


这个函数本身没问题，它将核心逻辑委托给了 withTokenRefresh。

第2步: withTokenRefresh 捕获第一次失败

withTokenRefresh 的代码是整个问题的核心。

Generated typescript
// auth-service.ts
static async withTokenRefresh<T>(apiCall: () => Promise<T>): Promise<T> {
  try {
    return await apiCall() // 1. 执行 apiClient.get('/api/user/quota')
  } catch (error) { // 2. 捕获到 401 错误
    // 3. 判断是否为认证错误
    if (this.isAuthError(error)) {
      // 4. 是认证错误，进入这个 if 块，尝试刷新 Token
      try {
        await this.refreshToken() // 5. 执行刷新逻辑
        // 如果刷新成功，这里会重试 apiCall()
        return await apiCall()
      } catch (refreshError) {
        // 6. 刷新 Token 的操作也失败了！进入这个 catch 块
        
        // 7. 执行登出，清除本地存储的 Token
        this.logout()

        // 8. 创建一个新的、特殊的错误对象
        const authFailedError = new Error('Authentication failed - refresh token expired')
        ;(authFailedError as any).code = 'REFRESH_TOKEN_EXPIRED'
        ;(authFailedError as any).shouldRedirect = true
        
        // 9. 抛出这个新的、特殊的错误
        throw authFailedError
      }
    }
    // 如果不是认证错误，直接抛出
    throw error
  }
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
TypeScript
IGNORE_WHEN_COPYING_END

流程解读：

apiCall() 执行，由于 access_token 过期，api.ts 中的 handleResponse 检测到 !response.ok 并正确地 throw error。

withTokenRefresh 的外层 catch (error) 捕获到这个错误。

this.isAuthError(error) 判断为 true，因为 error.message 会包含 "401" 或 "Unauthorized" 等关键字。

代码进入 if 块，尝试调用 this.refreshToken()。

refreshToken() 再次向后端发起请求，但由于 refresh_token 也无效，后端再次返回 401 或 400。

refreshToken() 中的 apiClient.post 抛出异常，这个异常被 withTokenRefresh 的内层 catch (refreshError) 捕获。

这是问题的关键点：在内层 catch 中，代码默默地调用了 this.logout()（这个函数只清除 localStorage，没有任何UI效果），然后抛出了一个全新的、自定义的错误 authFailedError。

第3步：错误在传递链中的最终走向

这个被抛出的 authFailedError 会被 getUserQuota 函数的 catch 块捕获，然后再次被 throw。最终，它到达了 page.tsx 的 catch 块。

Generated typescript
// page.tsx
} catch (error: any) {
  console.error('获取用户状态失败:', error)

  // 这里的 'error' 对象就是那个从 auth-service.ts 抛出的 authFailedError
  const { isAuthError: isAuth, shouldRedirect } = handleAuthError(error, () => {
    setShowAuthDialog(true)
  })

  if (isAuth && shouldRedirect) {
    // 这段代码应该被执行！
    setTimeout(() => {
      window.location.href = '/login'
    }, 2000)
  }
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
TypeScript
IGNORE_WHEN_COPYING_END
为什么UI没有反应？—— 问题的最终答案

既然代码看起来能把错误一路传递到 page.tsx，那为什么弹窗和跳转没有发生呢？

唯一的解释是：page.tsx 中调用的 handleAuthError 函数没有正确识别出我们从 auth-service.ts 精心构造并抛出的 authFailedError 对象。

handleAuthError 函数（在 lib/error-utils.ts 中，我们看不到它的源码）很可能只设计用来识别来自 api.ts 的原始HTTP错误，而没有兼容 auth-service.ts 中创建的这种特殊的、带有 .code = 'REFRESH_TOKEN_EXPIRED' 的自定义错误。

具体来说：

handleAuthError 可能在检查 error.response.status 或者一些它期望的特定属性。

而我们抛出的 authFailedError 是一个纯粹的 new Error(...)，它没有 .response 属性。

因此，handleAuthError 很可能返回了 { isAuthError: false, shouldRedirect: false }。

这导致 if (isAuth && shouldRedirect) 的判断为 false，弹窗和跳转逻辑被完全跳过。

总结与解决方案

您遇到的问题是一个非常精妙的逻辑断链：

底层 api.ts 正确地将HTTP 401错误转换为JS异常。

中间层 auth-service.ts 在处理“双重失败”时，将原始HTTP异常转换成了一个新的、自定义的认证异常 authFailedError。

顶层 page.tsx 使用了一个工具函数 handleAuthError，但这个函数无法识别中间层创建的那个自定义异常，导致它认为这“不是一个需要跳转的认证错误”。

如何修复：

您需要在 handleAuthError 函数（位于 lib/error-utils.ts）中增加逻辑，使其能够识别这个由 auth-service 抛出的特殊错误。

修改 lib/error-utils.ts 中的 handleAuthError (示例):

Generated typescript
// 在 lib/error-utils.ts
export function handleAuthError(error: any, onAuthError?: () => void) {
  // ... 原有的逻辑，可能是检查 error.response.status === 401 ...

  // 【新增的修复逻辑】
  // 检查是否是 auth-service 中定义的特殊错误
  if (error.code === 'REFRESH_TOKEN_EXPIRED') {
    if (onAuthError) {
      onAuthError(); // 触发弹窗
    }
    return { isAuthError: true, shouldRedirect: error.shouldRedirect || false };
  }

  // ... 原有的其他逻辑 ...
  
  // 如果都没有匹配上，返回 false
  return { isAuthError: false, shouldRedirect: false };
}
IGNORE_WHEN_COPYING_START
content_copy
download
Use code with caution.
TypeScript
IGNORE_WHEN_COPYING_END

通过这个修复，handleAuthError 就能正确识别出 refresh_token 过期这个“最终死刑”的信号，从而让 page.tsx 中已经写好的弹窗和跳转逻辑能够被顺利执行。