"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/error-utils.ts":
/*!****************************!*\
  !*** ./lib/error-utils.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AUTH_ERROR_CODES: () => (/* binding */ AUTH_ERROR_CODES),\n/* harmony export */   containsSensitiveInfo: () => (/* binding */ containsSensitiveInfo),\n/* harmony export */   getAuthErrorMessage: () => (/* binding */ getAuthErrorMessage),\n/* harmony export */   getErrorUIState: () => (/* binding */ getErrorUIState),\n/* harmony export */   handleAuthError: () => (/* binding */ handleAuthError),\n/* harmony export */   isAuthError: () => (/* binding */ isAuthError),\n/* harmony export */   isTokenExpiredError: () => (/* binding */ isTokenExpiredError),\n/* harmony export */   sanitizeErrorMessage: () => (/* binding */ sanitizeErrorMessage)\n/* harmony export */ });\n/**\n * 前端错误处理工具函数\n * 配合后端的结构化错误响应，提供统一的错误识别和处理\n * 包含前端敏感信息过滤功能，作为最后一道防线\n */ // 认证相关错误码\nconst AUTH_ERROR_CODES = {\n    TOKEN_EXPIRED: 'TOKEN_EXPIRED',\n    TOKEN_INVALID: 'TOKEN_INVALID',\n    TOKEN_TYPE_INVALID: 'TOKEN_TYPE_INVALID',\n    NO_TOKEN: 'NO_TOKEN',\n    AUTH_ERROR: 'AUTH_ERROR',\n    REFRESH_TOKEN_EXPIRED: 'REFRESH_TOKEN_EXPIRED'\n};\n// 前端敏感信息检测模式\nconst FRONTEND_SENSITIVE_PATTERNS = [\n    // 文件路径和系统信息\n    /[A-Za-z]:\\\\[\\w\\\\.-]+/g,\n    /\\/[\\w\\/.-]+\\.(js|ts|json|sql|env)/g,\n    /node_modules/gi,\n    /at\\s+[\\w.]+\\s+\\(/g,\n    // 网络和API信息\n    /https?:\\/\\/[\\w.-]+\\/[\\w\\/.-]*/g,\n    /localhost:\\d+/g,\n    /\\b(?:\\d{1,3}\\.){3}\\d{1,3}:\\d+\\b/g,\n    // 数据库和系统错误\n    /table\\s+[\"']?\\w+[\"']?/gi,\n    /column\\s+[\"']?\\w+[\"']?/gi,\n    /constraint\\s+[\"']?\\w+[\"']?/gi,\n    /error:\\s*\\w+error/gi,\n    /errno\\s*:\\s*\\d+/gi,\n    // 敏感关键词\n    /api[_-]?key/gi,\n    /secret/gi,\n    /password/gi\n];\n// 用户友好的错误消息映射\nconst FRONTEND_SAFE_MESSAGES = {\n    // 网络相关\n    'network': '网络连接异常，请检查网络后重试',\n    'timeout': '请求超时，请稍后重试',\n    'fetch': '网络请求失败，请稍后重试',\n    // 系统相关\n    'internal': '系统暂时繁忙，请稍后再试',\n    'server': '服务器暂时不可用，请稍后再试',\n    'database': '数据处理异常，请稍后重试',\n    // 认证相关\n    'auth': '认证失败，请重新登录',\n    'token': '登录会话已过期，请重新登录',\n    'unauthorized': '未授权访问，请先登录',\n    // 通用错误\n    'unknown': '发生未知错误，请稍后重试',\n    'default': '操作失败，请稍后重试'\n};\n/**\n * 检测错误消息中的敏感信息（前端最后防线）\n * @param message 错误消息\n * @returns 是否包含敏感信息\n */ function containsSensitiveInfo(message) {\n    if (!message || typeof message !== 'string') {\n        return false;\n    }\n    return FRONTEND_SENSITIVE_PATTERNS.some((pattern)=>pattern.test(message));\n}\n/**\n * 清理错误消息中的敏感信息\n * @param message 原始错误消息\n * @returns 清理后的错误消息\n */ function sanitizeErrorMessage(message) {\n    if (!message || typeof message !== 'string') {\n        return FRONTEND_SAFE_MESSAGES.default;\n    }\n    // 如果包含敏感信息，返回通用消息\n    if (containsSensitiveInfo(message)) {\n        // 根据消息内容返回相应的安全消息\n        const lowerMessage = message.toLowerCase();\n        if (lowerMessage.includes('network') || lowerMessage.includes('fetch')) {\n            return FRONTEND_SAFE_MESSAGES.network;\n        } else if (lowerMessage.includes('timeout')) {\n            return FRONTEND_SAFE_MESSAGES.timeout;\n        } else if (lowerMessage.includes('auth') || lowerMessage.includes('token')) {\n            return FRONTEND_SAFE_MESSAGES.auth;\n        } else if (lowerMessage.includes('server') || lowerMessage.includes('internal')) {\n            return FRONTEND_SAFE_MESSAGES.server;\n        } else if (lowerMessage.includes('database')) {\n            return FRONTEND_SAFE_MESSAGES.database;\n        } else {\n            return FRONTEND_SAFE_MESSAGES.unknown;\n        }\n    }\n    // 没有敏感信息，返回原始消息\n    return message;\n}\n/**\n * 判断是否为认证相关错误\n * @param error 错误对象\n * @returns 是否为认证错误\n */ function isAuthError(error) {\n    // 优先检查错误码（最可靠）\n    if (error === null || error === void 0 ? void 0 : error.code) {\n        return Object.values(AUTH_ERROR_CODES).includes(error.code);\n    }\n    // 兼容性检查：检查错误消息\n    if (error === null || error === void 0 ? void 0 : error.message) {\n        const message = error.message.toLowerCase();\n        return message.includes('token') || message.includes('expired') || message.includes('unauthorized') || message.includes('401') || message.includes('登录') || message.includes('refresh');\n    }\n    return false;\n}\n/**\n * 判断是否为token过期错误\n * @param error 错误对象\n * @returns 是否为token过期错误\n */ function isTokenExpiredError(error) {\n    // 优先检查错误码\n    if ((error === null || error === void 0 ? void 0 : error.code) === AUTH_ERROR_CODES.TOKEN_EXPIRED) {\n        return true;\n    }\n    // 兼容性检查：检查错误消息\n    if (error === null || error === void 0 ? void 0 : error.message) {\n        const message = error.message.toLowerCase();\n        return message.includes('token expired') || message.includes('expired') || message.includes('过期');\n    }\n    return false;\n}\n/**\n * 获取用户友好的错误消息\n * @param error 错误对象\n * @returns 用户友好的错误消息\n */ function getAuthErrorMessage(error) {\n    if (isAuthError(error)) {\n        // 【修复】检查错误对象上的shouldRedirect属性，默认为true\n        const shouldRedirect = (error === null || error === void 0 ? void 0 : error.shouldRedirect) !== undefined ? error.shouldRedirect : true;\n        return {\n            title: \"认证失败\",\n            description: \"会话已过期，正在跳转到登录页面...\",\n            shouldRedirect: shouldRedirect\n        };\n    }\n    // 业务错误或网络错误\n    return {\n        title: \"操作失败\",\n        description: (error === null || error === void 0 ? void 0 : error.message) || \"请检查网络连接后重试\",\n        shouldRedirect: false\n    };\n}\n/**\n * 统一的认证错误处理函数\n * @param error 错误对象\n * @param onAuthError 认证错误回调（可选）\n * @returns 处理结果\n */ function handleAuthError(error, onAuthError) {\n    const isAuth = isAuthError(error);\n    if (isAuth && onAuthError) {\n        onAuthError();\n    }\n    const errorInfo = getAuthErrorMessage(error);\n    return {\n        isAuthError: isAuth,\n        message: errorInfo.description,\n        shouldRedirect: errorInfo.shouldRedirect\n    };\n}\n/**\n * 为页面组件提供的错误处理hook辅助函数\n * @param error 错误对象\n * @returns UI状态更新信息\n */ function getErrorUIState(error) {\n    var _error_message;\n    if (isAuthError(error)) {\n        return {\n            statusDisplay: \"请重新登录\",\n            toastTitle: \"认证失败\",\n            toastDescription: \"会话已过期，正在跳转到登录页面...\",\n            variant: 'destructive'\n        };\n    }\n    // 检查是否为业务错误（如卡密无效等）\n    if (error === null || error === void 0 ? void 0 : (_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('卡密')) {\n        return {\n            statusDisplay: \"充值失败\",\n            toastTitle: \"充值失败\",\n            toastDescription: \"卡密无效或已使用，请检查后重试\",\n            variant: 'destructive'\n        };\n    }\n    // 网络或其他错误\n    return {\n        statusDisplay: \"获取失败\",\n        toastTitle: \"操作失败\",\n        toastDescription: (error === null || error === void 0 ? void 0 : error.message) || \"请检查网络连接后重试\",\n        variant: 'destructive'\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/error-utils.ts\n"));

/***/ })

});