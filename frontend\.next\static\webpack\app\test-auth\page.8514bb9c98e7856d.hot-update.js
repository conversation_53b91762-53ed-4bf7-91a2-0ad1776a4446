"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-auth/page",{

/***/ "(app-pages-browser)/./lib/error-utils.ts":
/*!****************************!*\
  !*** ./lib/error-utils.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AUTH_ERROR_CODES: () => (/* binding */ AUTH_ERROR_CODES),\n/* harmony export */   getAuthErrorMessage: () => (/* binding */ getAuthErrorMessage),\n/* harmony export */   getErrorUIState: () => (/* binding */ getErrorUIState),\n/* harmony export */   handleAuthError: () => (/* binding */ handleAuthError),\n/* harmony export */   isAuthError: () => (/* binding */ isAuthError),\n/* harmony export */   isTokenExpiredError: () => (/* binding */ isTokenExpiredError)\n/* harmony export */ });\n/**\n * 前端错误处理工具函数\n * 配合后端的结构化错误响应，提供统一的错误识别和处理\n * 包含前端敏感信息过滤功能，作为最后一道防线\n */ // 认证相关错误码\nconst AUTH_ERROR_CODES = {\n    TOKEN_EXPIRED: 'TOKEN_EXPIRED',\n    TOKEN_INVALID: 'TOKEN_INVALID',\n    TOKEN_TYPE_INVALID: 'TOKEN_TYPE_INVALID',\n    NO_TOKEN: 'NO_TOKEN',\n    AUTH_ERROR: 'AUTH_ERROR',\n    REFRESH_TOKEN_EXPIRED: 'REFRESH_TOKEN_EXPIRED'\n};\n// 前端敏感信息检测模式\nconst FRONTEND_SENSITIVE_PATTERNS = [\n    // 文件路径和系统信息\n    /[A-Za-z]:\\\\[\\w\\\\.-]+/g,\n    /\\/[\\w\\/.-]+\\.(js|ts|json|sql|env)/g,\n    /node_modules/gi,\n    /at\\s+[\\w.]+\\s+\\(/g,\n    // 网络和API信息\n    /https?:\\/\\/[\\w.-]+\\/[\\w\\/.-]*/g,\n    /localhost:\\d+/g,\n    /\\b(?:\\d{1,3}\\.){3}\\d{1,3}:\\d+\\b/g,\n    // 数据库和系统错误\n    /table\\s+[\"']?\\w+[\"']?/gi,\n    /column\\s+[\"']?\\w+[\"']?/gi,\n    /constraint\\s+[\"']?\\w+[\"']?/gi,\n    /error:\\s*\\w+error/gi,\n    /errno\\s*:\\s*\\d+/gi,\n    // 敏感关键词\n    /api[_-]?key/gi,\n    /secret/gi,\n    /password/gi\n];\n// 用户友好的错误消息映射\nconst FRONTEND_SAFE_MESSAGES = {\n    // 网络相关\n    'network': '网络连接异常，请检查网络后重试',\n    'timeout': '请求超时，请稍后重试',\n    'fetch': '网络请求失败，请稍后重试',\n    // 系统相关\n    'internal': '系统暂时繁忙，请稍后再试',\n    'server': '服务器暂时不可用，请稍后再试',\n    'database': '数据处理异常，请稍后重试',\n    // 认证相关\n    'auth': '认证失败，请重新登录',\n    'token': '登录会话已过期，请重新登录',\n    'unauthorized': '未授权访问，请先登录',\n    // 通用错误\n    'unknown': '发生未知错误，请稍后重试',\n    'default': '操作失败，请稍后重试'\n};\n/**\n * 判断是否为认证相关错误\n * @param error 错误对象\n * @returns 是否为认证错误\n */ function isAuthError(error) {\n    // 优先检查错误码（最可靠）\n    if (error === null || error === void 0 ? void 0 : error.code) {\n        return Object.values(AUTH_ERROR_CODES).includes(error.code);\n    }\n    // 兼容性检查：检查错误消息\n    if (error === null || error === void 0 ? void 0 : error.message) {\n        const message = error.message.toLowerCase();\n        return message.includes('token') || message.includes('expired') || message.includes('unauthorized') || message.includes('401') || message.includes('登录') || message.includes('refresh');\n    }\n    return false;\n}\n/**\n * 判断是否为token过期错误\n * @param error 错误对象\n * @returns 是否为token过期错误\n */ function isTokenExpiredError(error) {\n    // 优先检查错误码\n    if ((error === null || error === void 0 ? void 0 : error.code) === AUTH_ERROR_CODES.TOKEN_EXPIRED) {\n        return true;\n    }\n    // 兼容性检查：检查错误消息\n    if (error === null || error === void 0 ? void 0 : error.message) {\n        const message = error.message.toLowerCase();\n        return message.includes('token expired') || message.includes('expired') || message.includes('过期');\n    }\n    return false;\n}\n/**\n * 获取用户友好的错误消息\n * @param error 错误对象\n * @returns 用户友好的错误消息\n */ function getAuthErrorMessage(error) {\n    if (isAuthError(error)) {\n        // 【修复】检查错误对象上的shouldRedirect属性，默认为true\n        const shouldRedirect = (error === null || error === void 0 ? void 0 : error.shouldRedirect) !== undefined ? error.shouldRedirect : true;\n        return {\n            title: \"认证失败\",\n            description: \"会话已过期，正在跳转到登录页面...\",\n            shouldRedirect: shouldRedirect\n        };\n    }\n    // 业务错误或网络错误\n    return {\n        title: \"操作失败\",\n        description: (error === null || error === void 0 ? void 0 : error.message) || \"请检查网络连接后重试\",\n        shouldRedirect: false\n    };\n}\n/**\n * 统一的认证错误处理函数\n * @param error 错误对象\n * @param onAuthError 认证错误回调（可选）\n * @returns 处理结果\n */ function handleAuthError(error, onAuthError) {\n    const isAuth = isAuthError(error);\n    if (isAuth && onAuthError) {\n        onAuthError();\n    }\n    const errorInfo = getAuthErrorMessage(error);\n    return {\n        isAuthError: isAuth,\n        message: errorInfo.description,\n        shouldRedirect: errorInfo.shouldRedirect\n    };\n}\n/**\n * 为页面组件提供的错误处理hook辅助函数\n * @param error 错误对象\n * @returns UI状态更新信息\n */ function getErrorUIState(error) {\n    var _error_message;\n    if (isAuthError(error)) {\n        return {\n            statusDisplay: \"请重新登录\",\n            toastTitle: \"认证失败\",\n            toastDescription: \"会话已过期，正在跳转到登录页面...\",\n            variant: 'destructive'\n        };\n    }\n    // 检查是否为业务错误（如卡密无效等）\n    if (error === null || error === void 0 ? void 0 : (_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('卡密')) {\n        return {\n            statusDisplay: \"充值失败\",\n            toastTitle: \"充值失败\",\n            toastDescription: \"卡密无效或已使用，请检查后重试\",\n            variant: 'destructive'\n        };\n    }\n    // 网络或其他错误\n    return {\n        statusDisplay: \"获取失败\",\n        toastTitle: \"操作失败\",\n        toastDescription: (error === null || error === void 0 ? void 0 : error.message) || \"请检查网络连接后重试\",\n        variant: 'destructive'\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/error-utils.ts\n"));

/***/ })

});