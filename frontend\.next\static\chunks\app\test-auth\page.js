/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/test-auth/page"],{

/***/ "(app-pages-browser)/./app/test-auth/page.tsx":
/*!********************************!*\
  !*** ./app/test-auth/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestAuthPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/auth-service */ \"(app-pages-browser)/./lib/auth-service.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_error_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/error-utils */ \"(app-pages-browser)/./lib/error-utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction TestAuthPage() {\n    _s();\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showAuthDialog, setShowAuthDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const testInvalidToken = async ()=>{\n        try {\n            // 设置一个无效的token\n            _lib_api__WEBPACK_IMPORTED_MODULE_5__.TokenManager.setTokens(\"invalid_token\", \"invalid_refresh_token\", \"<EMAIL>\");\n            // 尝试获取用户配额\n            const data = await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.auth.getUserQuota();\n            setResult(\"成功获取数据: \" + JSON.stringify(data));\n        } catch (error) {\n            console.error('测试错误:', error);\n            setResult(\"捕获错误: \" + error.message + \" (code: \" + (error.code || \"无\") + \")\");\n            // 使用错误处理函数\n            const { isAuthError: isAuth, shouldRedirect } = (0,_lib_error_utils__WEBPACK_IMPORTED_MODULE_7__.handleAuthError)(error, ()=>{\n                setShowAuthDialog(true);\n                toast({\n                    title: \"认证失败\",\n                    description: \"会话已过期，正在跳转到登录页面...\",\n                    variant: \"destructive\"\n                });\n            });\n            if (isAuth && shouldRedirect) {\n                setTimeout(()=>{\n                    window.location.href = '/login';\n                }, 2000);\n            }\n        }\n    };\n    const testNoToken = async ()=>{\n        try {\n            // 清除所有token\n            _lib_api__WEBPACK_IMPORTED_MODULE_5__.TokenManager.clearTokens();\n            // 尝试获取用户配额\n            const data = await _lib_auth_service__WEBPACK_IMPORTED_MODULE_4__.auth.getUserQuota();\n            setResult(\"成功获取数据: \" + JSON.stringify(data));\n        } catch (error) {\n            console.error('测试错误:', error);\n            setResult(\"捕获错误: \" + error.message + \" (code: \" + (error.code || \"无\") + \")\");\n            // 使用错误处理函数\n            const { isAuthError: isAuth, shouldRedirect } = (0,_lib_error_utils__WEBPACK_IMPORTED_MODULE_7__.handleAuthError)(error, ()=>{\n                setShowAuthDialog(true);\n                toast({\n                    title: \"认证失败\",\n                    description: \"会话已过期，正在跳转到登录页面...\",\n                    variant: \"destructive\"\n                });\n            });\n            if (isAuth && shouldRedirect) {\n                setTimeout(()=>{\n                    window.location.href = '/login';\n                }, 2000);\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            className: \"max-w-2xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                        children: \"认证错误处理测试\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\test-auth\\\\page.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\test-auth\\\\page.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: testInvalidToken,\n                                    variant: \"outline\",\n                                    children: \"测试无效Token\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\test-auth\\\\page.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: testNoToken,\n                                    variant: \"outline\",\n                                    children: \"测试无Token\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\test-auth\\\\page.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this),\n                        result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 bg-gray-100 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold mb-2\",\n                                    children: \"测试结果:\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\test-auth\\\\page.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                    className: \"text-sm whitespace-pre-wrap\",\n                                    children: result\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\test-auth\\\\page.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 13\n                        }, this),\n                        showAuthDialog && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 bg-red-100 border border-red-300 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-red-800\",\n                                    children: \"认证错误弹窗已触发!\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\test-auth\\\\page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600\",\n                                    children: \"正在准备跳转到登录页面...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\test-auth\\\\page.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\test-auth\\\\page.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\test-auth\\\\page.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\test-auth\\\\page.tsx\",\n            lineNumber: 78,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\myaitts\\\\frontend\\\\app\\\\test-auth\\\\page.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n_s(TestAuthPage, \"2Sdo10tZKGU17GGklU0cq98V8T4=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = TestAuthPage;\nvar _c;\n$RefreshReg$(_c, \"TestAuthPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC90ZXN0LWF1dGgvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBRWdDO0FBQ2U7QUFDZ0M7QUFDdEM7QUFDRDtBQUNJO0FBQ087QUFFcEMsU0FBU1U7O0lBQ3RCLE1BQU0sQ0FBQ0MsUUFBUUMsVUFBVSxHQUFHWiwrQ0FBUUEsQ0FBUztJQUM3QyxNQUFNLENBQUNhLGdCQUFnQkMsa0JBQWtCLEdBQUdkLCtDQUFRQSxDQUFDO0lBQ3JELE1BQU0sRUFBRWUsS0FBSyxFQUFFLEdBQUdQLDBEQUFRQTtJQUUxQixNQUFNUSxtQkFBbUI7UUFDdkIsSUFBSTtZQUNGLGVBQWU7WUFDZlQsa0RBQVlBLENBQUNVLFNBQVMsQ0FBQyxpQkFBaUIseUJBQXlCO1lBRWpFLFdBQVc7WUFDWCxNQUFNQyxPQUFPLE1BQU1aLG1EQUFJQSxDQUFDYSxZQUFZO1lBQ3BDUCxVQUFVLGFBQWFRLEtBQUtDLFNBQVMsQ0FBQ0g7UUFDeEMsRUFBRSxPQUFPSSxPQUFZO1lBQ25CQyxRQUFRRCxLQUFLLENBQUMsU0FBU0E7WUFDdkJWLFVBQVUsV0FBV1UsTUFBTUUsT0FBTyxHQUFHLGFBQWNGLENBQUFBLE1BQU1HLElBQUksSUFBSSxHQUFFLElBQUs7WUFFeEUsV0FBVztZQUNYLE1BQU0sRUFBRUMsYUFBYUMsTUFBTSxFQUFFQyxjQUFjLEVBQUUsR0FBR25CLGlFQUFlQSxDQUFDYSxPQUFPO2dCQUNyRVIsa0JBQWtCO2dCQUNsQkMsTUFBTTtvQkFDSmMsT0FBTztvQkFDUEMsYUFBYTtvQkFDYkMsU0FBUztnQkFDWDtZQUNGO1lBRUEsSUFBSUosVUFBVUMsZ0JBQWdCO2dCQUM1QkksV0FBVztvQkFDVEMsT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUc7Z0JBQ3pCLEdBQUc7WUFDTDtRQUNGO0lBQ0Y7SUFFQSxNQUFNQyxjQUFjO1FBQ2xCLElBQUk7WUFDRixZQUFZO1lBQ1o3QixrREFBWUEsQ0FBQzhCLFdBQVc7WUFFeEIsV0FBVztZQUNYLE1BQU1uQixPQUFPLE1BQU1aLG1EQUFJQSxDQUFDYSxZQUFZO1lBQ3BDUCxVQUFVLGFBQWFRLEtBQUtDLFNBQVMsQ0FBQ0g7UUFDeEMsRUFBRSxPQUFPSSxPQUFZO1lBQ25CQyxRQUFRRCxLQUFLLENBQUMsU0FBU0E7WUFDdkJWLFVBQVUsV0FBV1UsTUFBTUUsT0FBTyxHQUFHLGFBQWNGLENBQUFBLE1BQU1HLElBQUksSUFBSSxHQUFFLElBQUs7WUFFeEUsV0FBVztZQUNYLE1BQU0sRUFBRUMsYUFBYUMsTUFBTSxFQUFFQyxjQUFjLEVBQUUsR0FBR25CLGlFQUFlQSxDQUFDYSxPQUFPO2dCQUNyRVIsa0JBQWtCO2dCQUNsQkMsTUFBTTtvQkFDSmMsT0FBTztvQkFDUEMsYUFBYTtvQkFDYkMsU0FBUztnQkFDWDtZQUNGO1lBRUEsSUFBSUosVUFBVUMsZ0JBQWdCO2dCQUM1QkksV0FBVztvQkFDVEMsT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUc7Z0JBQ3pCLEdBQUc7WUFDTDtRQUNGO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ0c7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ3JDLHFEQUFJQTtZQUFDcUMsV0FBVTs7OEJBQ2QsOERBQUNuQywyREFBVUE7OEJBQ1QsNEVBQUNDLDBEQUFTQTtrQ0FBQzs7Ozs7Ozs7Ozs7OEJBRWIsOERBQUNGLDREQUFXQTtvQkFBQ29DLFdBQVU7O3NDQUNyQiw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDdEMseURBQU1BO29DQUFDdUMsU0FBU3hCO29DQUFrQmUsU0FBUTs4Q0FBVTs7Ozs7OzhDQUdyRCw4REFBQzlCLHlEQUFNQTtvQ0FBQ3VDLFNBQVNKO29DQUFhTCxTQUFROzhDQUFVOzs7Ozs7Ozs7Ozs7d0JBS2pEcEIsd0JBQ0MsOERBQUMyQjs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNFO29DQUFHRixXQUFVOzhDQUFxQjs7Ozs7OzhDQUNuQyw4REFBQ0c7b0NBQUlILFdBQVU7OENBQStCNUI7Ozs7Ozs7Ozs7Ozt3QkFJakRFLGdDQUNDLDhEQUFDeUI7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRTtvQ0FBR0YsV0FBVTs4Q0FBNkI7Ozs7Ozs4Q0FDM0MsOERBQUNJO29DQUFFSixXQUFVOzhDQUFlOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU8xQztHQWxHd0I3Qjs7UUFHSkYsc0RBQVFBOzs7S0FISkUiLCJzb3VyY2VzIjpbIkQ6XFxteWFpdHRzXFxmcm9udGVuZFxcYXBwXFx0ZXN0LWF1dGhcXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYnV0dG9uXCJcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2NhcmRcIlxuaW1wb3J0IHsgYXV0aCB9IGZyb20gXCJAL2xpYi9hdXRoLXNlcnZpY2VcIlxuaW1wb3J0IHsgVG9rZW5NYW5hZ2VyIH0gZnJvbSBcIkAvbGliL2FwaVwiXG5pbXBvcnQgeyB1c2VUb2FzdCB9IGZyb20gXCJAL2hvb2tzL3VzZS10b2FzdFwiXG5pbXBvcnQgeyBoYW5kbGVBdXRoRXJyb3IgfSBmcm9tIFwiQC9saWIvZXJyb3ItdXRpbHNcIlxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBUZXN0QXV0aFBhZ2UoKSB7XG4gIGNvbnN0IFtyZXN1bHQsIHNldFJlc3VsdF0gPSB1c2VTdGF0ZTxzdHJpbmc+KFwiXCIpXG4gIGNvbnN0IFtzaG93QXV0aERpYWxvZywgc2V0U2hvd0F1dGhEaWFsb2ddID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IHsgdG9hc3QgfSA9IHVzZVRvYXN0KClcblxuICBjb25zdCB0ZXN0SW52YWxpZFRva2VuID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICAvLyDorr7nva7kuIDkuKrml6DmlYjnmoR0b2tlblxuICAgICAgVG9rZW5NYW5hZ2VyLnNldFRva2VucyhcImludmFsaWRfdG9rZW5cIiwgXCJpbnZhbGlkX3JlZnJlc2hfdG9rZW5cIiwgXCJ0ZXN0QGV4YW1wbGUuY29tXCIpXG4gICAgICBcbiAgICAgIC8vIOWwneivleiOt+WPlueUqOaIt+mFjeminVxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IGF1dGguZ2V0VXNlclF1b3RhKClcbiAgICAgIHNldFJlc3VsdChcIuaIkOWKn+iOt+WPluaVsOaNrjogXCIgKyBKU09OLnN0cmluZ2lmeShkYXRhKSlcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfmtYvor5XplJnor686JywgZXJyb3IpXG4gICAgICBzZXRSZXN1bHQoXCLmjZXojrfplJnor686IFwiICsgZXJyb3IubWVzc2FnZSArIFwiIChjb2RlOiBcIiArIChlcnJvci5jb2RlIHx8IFwi5pegXCIpICsgXCIpXCIpXG4gICAgICBcbiAgICAgIC8vIOS9v+eUqOmUmeivr+WkhOeQhuWHveaVsFxuICAgICAgY29uc3QgeyBpc0F1dGhFcnJvcjogaXNBdXRoLCBzaG91bGRSZWRpcmVjdCB9ID0gaGFuZGxlQXV0aEVycm9yKGVycm9yLCAoKSA9PiB7XG4gICAgICAgIHNldFNob3dBdXRoRGlhbG9nKHRydWUpXG4gICAgICAgIHRvYXN0KHtcbiAgICAgICAgICB0aXRsZTogXCLorqTor4HlpLHotKVcIixcbiAgICAgICAgICBkZXNjcmlwdGlvbjogXCLkvJror53lt7Lov4fmnJ/vvIzmraPlnKjot7PovazliLDnmbvlvZXpobXpnaIuLi5cIixcbiAgICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCIsXG4gICAgICAgIH0pXG4gICAgICB9KVxuICAgICAgXG4gICAgICBpZiAoaXNBdXRoICYmIHNob3VsZFJlZGlyZWN0KSB7XG4gICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gJy9sb2dpbidcbiAgICAgICAgfSwgMjAwMClcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICBjb25zdCB0ZXN0Tm9Ub2tlbiA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgLy8g5riF6Zmk5omA5pyJdG9rZW5cbiAgICAgIFRva2VuTWFuYWdlci5jbGVhclRva2VucygpXG4gICAgICBcbiAgICAgIC8vIOWwneivleiOt+WPlueUqOaIt+mFjeminVxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IGF1dGguZ2V0VXNlclF1b3RhKClcbiAgICAgIHNldFJlc3VsdChcIuaIkOWKn+iOt+WPluaVsOaNrjogXCIgKyBKU09OLnN0cmluZ2lmeShkYXRhKSlcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfmtYvor5XplJnor686JywgZXJyb3IpXG4gICAgICBzZXRSZXN1bHQoXCLmjZXojrfplJnor686IFwiICsgZXJyb3IubWVzc2FnZSArIFwiIChjb2RlOiBcIiArIChlcnJvci5jb2RlIHx8IFwi5pegXCIpICsgXCIpXCIpXG4gICAgICBcbiAgICAgIC8vIOS9v+eUqOmUmeivr+WkhOeQhuWHveaVsFxuICAgICAgY29uc3QgeyBpc0F1dGhFcnJvcjogaXNBdXRoLCBzaG91bGRSZWRpcmVjdCB9ID0gaGFuZGxlQXV0aEVycm9yKGVycm9yLCAoKSA9PiB7XG4gICAgICAgIHNldFNob3dBdXRoRGlhbG9nKHRydWUpXG4gICAgICAgIHRvYXN0KHtcbiAgICAgICAgICB0aXRsZTogXCLorqTor4HlpLHotKVcIiwgXG4gICAgICAgICAgZGVzY3JpcHRpb246IFwi5Lya6K+d5bey6L+H5pyf77yM5q2j5Zyo6Lez6L2s5Yiw55m75b2V6aG16Z2iLi4uXCIsXG4gICAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiLFxuICAgICAgICB9KVxuICAgICAgfSlcbiAgICAgIFxuICAgICAgaWYgKGlzQXV0aCAmJiBzaG91bGRSZWRpcmVjdCkge1xuICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9ICcvbG9naW4nXG4gICAgICAgIH0sIDIwMDApXG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBteC1hdXRvIHAtNFwiPlxuICAgICAgPENhcmQgY2xhc3NOYW1lPVwibWF4LXctMnhsIG14LWF1dG9cIj5cbiAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRUaXRsZT7orqTor4HplJnor6/lpITnkIbmtYvor5U8L0NhcmRUaXRsZT5cbiAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgIDxCdXR0b24gb25DbGljaz17dGVzdEludmFsaWRUb2tlbn0gdmFyaWFudD1cIm91dGxpbmVcIj5cbiAgICAgICAgICAgICAg5rWL6K+V5peg5pWIVG9rZW5cbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXt0ZXN0Tm9Ub2tlbn0gdmFyaWFudD1cIm91dGxpbmVcIj5cbiAgICAgICAgICAgICAg5rWL6K+V5pegVG9rZW5cbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIFxuICAgICAgICAgIHtyZXN1bHQgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgYmctZ3JheS0xMDAgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCBtYi0yXCI+5rWL6K+V57uT5p6cOjwvaDM+XG4gICAgICAgICAgICAgIDxwcmUgY2xhc3NOYW1lPVwidGV4dC1zbSB3aGl0ZXNwYWNlLXByZS13cmFwXCI+e3Jlc3VsdH08L3ByZT5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgICAgXG4gICAgICAgICAge3Nob3dBdXRoRGlhbG9nICYmIChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IGJnLXJlZC0xMDAgYm9yZGVyIGJvcmRlci1yZWQtMzAwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1yZWQtODAwXCI+6K6k6K+B6ZSZ6K+v5by556qX5bey6Kem5Y+RITwvaDM+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcmVkLTYwMFwiPuato+WcqOWHhuWkh+i3s+i9rOWIsOeZu+W9lemhtemdoi4uLjwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICA8L0NhcmQ+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsIkJ1dHRvbiIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJhdXRoIiwiVG9rZW5NYW5hZ2VyIiwidXNlVG9hc3QiLCJoYW5kbGVBdXRoRXJyb3IiLCJUZXN0QXV0aFBhZ2UiLCJyZXN1bHQiLCJzZXRSZXN1bHQiLCJzaG93QXV0aERpYWxvZyIsInNldFNob3dBdXRoRGlhbG9nIiwidG9hc3QiLCJ0ZXN0SW52YWxpZFRva2VuIiwic2V0VG9rZW5zIiwiZGF0YSIsImdldFVzZXJRdW90YSIsIkpTT04iLCJzdHJpbmdpZnkiLCJlcnJvciIsImNvbnNvbGUiLCJtZXNzYWdlIiwiY29kZSIsImlzQXV0aEVycm9yIiwiaXNBdXRoIiwic2hvdWxkUmVkaXJlY3QiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwidmFyaWFudCIsInNldFRpbWVvdXQiLCJ3aW5kb3ciLCJsb2NhdGlvbiIsImhyZWYiLCJ0ZXN0Tm9Ub2tlbiIsImNsZWFyVG9rZW5zIiwiZGl2IiwiY2xhc3NOYW1lIiwib25DbGljayIsImgzIiwicHJlIiwicCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/test-auth/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(app-pages-browser)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(app-pages-browser)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, variant, size, asChild = false, ...props } = param;\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\myaitts\\\\frontend\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\n_c1 = Button;\nButton.displayName = \"Button\";\n\nvar _c, _c1;\n$RefreshReg$(_c, \"Button$React.forwardRef\");\n$RefreshReg$(_c1, \"Button\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/button.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\myaitts\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined);\n});\n_c1 = Card;\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c2 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\myaitts\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined);\n});\n_c3 = CardHeader;\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c4 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\myaitts\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined);\n});\n_c5 = CardTitle;\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c6 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\myaitts\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined);\n});\n_c7 = CardDescription;\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c8 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\myaitts\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined);\n});\n_c9 = CardContent;\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c10 = (param, ref)=>{\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\myaitts\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined);\n});\n_c11 = CardFooter;\nCardFooter.displayName = \"CardFooter\";\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11;\n$RefreshReg$(_c, \"Card$React.forwardRef\");\n$RefreshReg$(_c1, \"Card\");\n$RefreshReg$(_c2, \"CardHeader$React.forwardRef\");\n$RefreshReg$(_c3, \"CardHeader\");\n$RefreshReg$(_c4, \"CardTitle$React.forwardRef\");\n$RefreshReg$(_c5, \"CardTitle\");\n$RefreshReg$(_c6, \"CardDescription$React.forwardRef\");\n$RefreshReg$(_c7, \"CardDescription\");\n$RefreshReg$(_c8, \"CardContent$React.forwardRef\");\n$RefreshReg$(_c9, \"CardContent\");\n$RefreshReg$(_c10, \"CardFooter$React.forwardRef\");\n$RefreshReg$(_c11, \"CardFooter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/card.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./hooks/use-toast.ts":
/*!****************************!*\
  !*** ./hooks/use-toast.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ reducer,useToast,toast auto */ // Inspired by react-hot-toast library\n\nconst TOAST_LIMIT = 1;\nconst TOAST_REMOVE_DELAY = 1000000;\nconst actionTypes = {\n    ADD_TOAST: \"ADD_TOAST\",\n    UPDATE_TOAST: \"UPDATE_TOAST\",\n    DISMISS_TOAST: \"DISMISS_TOAST\",\n    REMOVE_TOAST: \"REMOVE_TOAST\"\n};\nlet count = 0;\nfunction genId() {\n    count = (count + 1) % Number.MAX_SAFE_INTEGER;\n    return count.toString();\n}\nconst toastTimeouts = new Map();\nconst addToRemoveQueue = (toastId)=>{\n    if (toastTimeouts.has(toastId)) {\n        return;\n    }\n    const timeout = setTimeout(()=>{\n        toastTimeouts.delete(toastId);\n        dispatch({\n            type: \"REMOVE_TOAST\",\n            toastId: toastId\n        });\n    }, TOAST_REMOVE_DELAY);\n    toastTimeouts.set(toastId, timeout);\n};\nconst reducer = (state, action)=>{\n    switch(action.type){\n        case \"ADD_TOAST\":\n            return {\n                ...state,\n                toasts: [\n                    action.toast,\n                    ...state.toasts\n                ].slice(0, TOAST_LIMIT)\n            };\n        case \"UPDATE_TOAST\":\n            return {\n                ...state,\n                toasts: state.toasts.map((t)=>t.id === action.toast.id ? {\n                        ...t,\n                        ...action.toast\n                    } : t)\n            };\n        case \"DISMISS_TOAST\":\n            {\n                const { toastId } = action;\n                // ! Side effects ! - This could be extracted into a dismissToast() action,\n                // but I'll keep it here for simplicity\n                if (toastId) {\n                    addToRemoveQueue(toastId);\n                } else {\n                    state.toasts.forEach((toast)=>{\n                        addToRemoveQueue(toast.id);\n                    });\n                }\n                return {\n                    ...state,\n                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {\n                            ...t,\n                            open: false\n                        } : t)\n                };\n            }\n        case \"REMOVE_TOAST\":\n            if (action.toastId === undefined) {\n                return {\n                    ...state,\n                    toasts: []\n                };\n            }\n            return {\n                ...state,\n                toasts: state.toasts.filter((t)=>t.id !== action.toastId)\n            };\n    }\n};\nconst listeners = [];\nlet memoryState = {\n    toasts: []\n};\nfunction dispatch(action) {\n    memoryState = reducer(memoryState, action);\n    listeners.forEach((listener)=>{\n        listener(memoryState);\n    });\n}\nfunction toast(param) {\n    let { ...props } = param;\n    const id = genId();\n    const update = (props)=>dispatch({\n            type: \"UPDATE_TOAST\",\n            toast: {\n                ...props,\n                id\n            }\n        });\n    const dismiss = ()=>dispatch({\n            type: \"DISMISS_TOAST\",\n            toastId: id\n        });\n    dispatch({\n        type: \"ADD_TOAST\",\n        toast: {\n            ...props,\n            id,\n            open: true,\n            onOpenChange: (open)=>{\n                if (!open) dismiss();\n            }\n        }\n    });\n    return {\n        id: id,\n        dismiss,\n        update\n    };\n}\nfunction useToast() {\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(memoryState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useToast.useEffect\": ()=>{\n            listeners.push(setState);\n            return ({\n                \"useToast.useEffect\": ()=>{\n                    const index = listeners.indexOf(setState);\n                    if (index > -1) {\n                        listeners.splice(index, 1);\n                    }\n                }\n            })[\"useToast.useEffect\"];\n        }\n    }[\"useToast.useEffect\"], [\n        state\n    ]);\n    return {\n        ...state,\n        toast,\n        dismiss: (toastId)=>dispatch({\n                type: \"DISMISS_TOAST\",\n                toastId\n            })\n    };\n}\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2hvb2tzL3VzZS10b2FzdC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs0RUFFQSxzQ0FBc0M7QUFDUjtBQU85QixNQUFNQyxjQUFjO0FBQ3BCLE1BQU1DLHFCQUFxQjtBQVMzQixNQUFNQyxjQUFjO0lBQ2xCQyxXQUFXO0lBQ1hDLGNBQWM7SUFDZEMsZUFBZTtJQUNmQyxjQUFjO0FBQ2hCO0FBRUEsSUFBSUMsUUFBUTtBQUVaLFNBQVNDO0lBQ1BELFFBQVEsQ0FBQ0EsUUFBUSxLQUFLRSxPQUFPQyxnQkFBZ0I7SUFDN0MsT0FBT0gsTUFBTUksUUFBUTtBQUN2QjtBQTBCQSxNQUFNQyxnQkFBZ0IsSUFBSUM7QUFFMUIsTUFBTUMsbUJBQW1CLENBQUNDO0lBQ3hCLElBQUlILGNBQWNJLEdBQUcsQ0FBQ0QsVUFBVTtRQUM5QjtJQUNGO0lBRUEsTUFBTUUsVUFBVUMsV0FBVztRQUN6Qk4sY0FBY08sTUFBTSxDQUFDSjtRQUNyQkssU0FBUztZQUNQQyxNQUFNO1lBQ05OLFNBQVNBO1FBQ1g7SUFDRixHQUFHZDtJQUVIVyxjQUFjVSxHQUFHLENBQUNQLFNBQVNFO0FBQzdCO0FBRU8sTUFBTU0sVUFBVSxDQUFDQyxPQUFjQztJQUNwQyxPQUFRQSxPQUFPSixJQUFJO1FBQ2pCLEtBQUs7WUFDSCxPQUFPO2dCQUNMLEdBQUdHLEtBQUs7Z0JBQ1JFLFFBQVE7b0JBQUNELE9BQU9FLEtBQUs7dUJBQUtILE1BQU1FLE1BQU07aUJBQUMsQ0FBQ0UsS0FBSyxDQUFDLEdBQUc1QjtZQUNuRDtRQUVGLEtBQUs7WUFDSCxPQUFPO2dCQUNMLEdBQUd3QixLQUFLO2dCQUNSRSxRQUFRRixNQUFNRSxNQUFNLENBQUNHLEdBQUcsQ0FBQyxDQUFDQyxJQUN4QkEsRUFBRUMsRUFBRSxLQUFLTixPQUFPRSxLQUFLLENBQUNJLEVBQUUsR0FBRzt3QkFBRSxHQUFHRCxDQUFDO3dCQUFFLEdBQUdMLE9BQU9FLEtBQUs7b0JBQUMsSUFBSUc7WUFFM0Q7UUFFRixLQUFLO1lBQWlCO2dCQUNwQixNQUFNLEVBQUVmLE9BQU8sRUFBRSxHQUFHVTtnQkFFcEIsMkVBQTJFO2dCQUMzRSx1Q0FBdUM7Z0JBQ3ZDLElBQUlWLFNBQVM7b0JBQ1hELGlCQUFpQkM7Z0JBQ25CLE9BQU87b0JBQ0xTLE1BQU1FLE1BQU0sQ0FBQ00sT0FBTyxDQUFDLENBQUNMO3dCQUNwQmIsaUJBQWlCYSxNQUFNSSxFQUFFO29CQUMzQjtnQkFDRjtnQkFFQSxPQUFPO29CQUNMLEdBQUdQLEtBQUs7b0JBQ1JFLFFBQVFGLE1BQU1FLE1BQU0sQ0FBQ0csR0FBRyxDQUFDLENBQUNDLElBQ3hCQSxFQUFFQyxFQUFFLEtBQUtoQixXQUFXQSxZQUFZa0IsWUFDNUI7NEJBQ0UsR0FBR0gsQ0FBQzs0QkFDSkksTUFBTTt3QkFDUixJQUNBSjtnQkFFUjtZQUNGO1FBQ0EsS0FBSztZQUNILElBQUlMLE9BQU9WLE9BQU8sS0FBS2tCLFdBQVc7Z0JBQ2hDLE9BQU87b0JBQ0wsR0FBR1QsS0FBSztvQkFDUkUsUUFBUSxFQUFFO2dCQUNaO1lBQ0Y7WUFDQSxPQUFPO2dCQUNMLEdBQUdGLEtBQUs7Z0JBQ1JFLFFBQVFGLE1BQU1FLE1BQU0sQ0FBQ1MsTUFBTSxDQUFDLENBQUNMLElBQU1BLEVBQUVDLEVBQUUsS0FBS04sT0FBT1YsT0FBTztZQUM1RDtJQUNKO0FBQ0YsRUFBQztBQUVELE1BQU1xQixZQUEyQyxFQUFFO0FBRW5ELElBQUlDLGNBQXFCO0lBQUVYLFFBQVEsRUFBRTtBQUFDO0FBRXRDLFNBQVNOLFNBQVNLLE1BQWM7SUFDOUJZLGNBQWNkLFFBQVFjLGFBQWFaO0lBQ25DVyxVQUFVSixPQUFPLENBQUMsQ0FBQ007UUFDakJBLFNBQVNEO0lBQ1g7QUFDRjtBQUlBLFNBQVNWLE1BQU0sS0FBbUI7UUFBbkIsRUFBRSxHQUFHWSxPQUFjLEdBQW5CO0lBQ2IsTUFBTVIsS0FBS3ZCO0lBRVgsTUFBTWdDLFNBQVMsQ0FBQ0QsUUFDZG5CLFNBQVM7WUFDUEMsTUFBTTtZQUNOTSxPQUFPO2dCQUFFLEdBQUdZLEtBQUs7Z0JBQUVSO1lBQUc7UUFDeEI7SUFDRixNQUFNVSxVQUFVLElBQU1yQixTQUFTO1lBQUVDLE1BQU07WUFBaUJOLFNBQVNnQjtRQUFHO0lBRXBFWCxTQUFTO1FBQ1BDLE1BQU07UUFDTk0sT0FBTztZQUNMLEdBQUdZLEtBQUs7WUFDUlI7WUFDQUcsTUFBTTtZQUNOUSxjQUFjLENBQUNSO2dCQUNiLElBQUksQ0FBQ0EsTUFBTU87WUFDYjtRQUNGO0lBQ0Y7SUFFQSxPQUFPO1FBQ0xWLElBQUlBO1FBQ0pVO1FBQ0FEO0lBQ0Y7QUFDRjtBQUVBLFNBQVNHO0lBQ1AsTUFBTSxDQUFDbkIsT0FBT29CLFNBQVMsR0FBRzdDLDJDQUFjLENBQVFzQztJQUVoRHRDLDRDQUFlOzhCQUFDO1lBQ2RxQyxVQUFVVyxJQUFJLENBQUNIO1lBQ2Y7c0NBQU87b0JBQ0wsTUFBTUksUUFBUVosVUFBVWEsT0FBTyxDQUFDTDtvQkFDaEMsSUFBSUksUUFBUSxDQUFDLEdBQUc7d0JBQ2RaLFVBQVVjLE1BQU0sQ0FBQ0YsT0FBTztvQkFDMUI7Z0JBQ0Y7O1FBQ0Y7NkJBQUc7UUFBQ3hCO0tBQU07SUFFVixPQUFPO1FBQ0wsR0FBR0EsS0FBSztRQUNSRztRQUNBYyxTQUFTLENBQUMxQixVQUFxQkssU0FBUztnQkFBRUMsTUFBTTtnQkFBaUJOO1lBQVE7SUFDM0U7QUFDRjtBQUUwQiIsInNvdXJjZXMiOlsiRDpcXG15YWl0dHNcXGZyb250ZW5kXFxob29rc1xcdXNlLXRvYXN0LnRzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbi8vIEluc3BpcmVkIGJ5IHJlYWN0LWhvdC10b2FzdCBsaWJyYXJ5XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuXG5pbXBvcnQgdHlwZSB7XG4gIFRvYXN0QWN0aW9uRWxlbWVudCxcbiAgVG9hc3RQcm9wcyxcbn0gZnJvbSBcIkAvY29tcG9uZW50cy91aS90b2FzdFwiXG5cbmNvbnN0IFRPQVNUX0xJTUlUID0gMVxuY29uc3QgVE9BU1RfUkVNT1ZFX0RFTEFZID0gMTAwMDAwMFxuXG50eXBlIFRvYXN0ZXJUb2FzdCA9IFRvYXN0UHJvcHMgJiB7XG4gIGlkOiBzdHJpbmdcbiAgdGl0bGU/OiBSZWFjdC5SZWFjdE5vZGVcbiAgZGVzY3JpcHRpb24/OiBSZWFjdC5SZWFjdE5vZGVcbiAgYWN0aW9uPzogVG9hc3RBY3Rpb25FbGVtZW50XG59XG5cbmNvbnN0IGFjdGlvblR5cGVzID0ge1xuICBBRERfVE9BU1Q6IFwiQUREX1RPQVNUXCIsXG4gIFVQREFURV9UT0FTVDogXCJVUERBVEVfVE9BU1RcIixcbiAgRElTTUlTU19UT0FTVDogXCJESVNNSVNTX1RPQVNUXCIsXG4gIFJFTU9WRV9UT0FTVDogXCJSRU1PVkVfVE9BU1RcIixcbn0gYXMgY29uc3RcblxubGV0IGNvdW50ID0gMFxuXG5mdW5jdGlvbiBnZW5JZCgpIHtcbiAgY291bnQgPSAoY291bnQgKyAxKSAlIE51bWJlci5NQVhfU0FGRV9JTlRFR0VSXG4gIHJldHVybiBjb3VudC50b1N0cmluZygpXG59XG5cbnR5cGUgQWN0aW9uVHlwZSA9IHR5cGVvZiBhY3Rpb25UeXBlc1xuXG50eXBlIEFjdGlvbiA9XG4gIHwge1xuICAgICAgdHlwZTogQWN0aW9uVHlwZVtcIkFERF9UT0FTVFwiXVxuICAgICAgdG9hc3Q6IFRvYXN0ZXJUb2FzdFxuICAgIH1cbiAgfCB7XG4gICAgICB0eXBlOiBBY3Rpb25UeXBlW1wiVVBEQVRFX1RPQVNUXCJdXG4gICAgICB0b2FzdDogUGFydGlhbDxUb2FzdGVyVG9hc3Q+XG4gICAgfVxuICB8IHtcbiAgICAgIHR5cGU6IEFjdGlvblR5cGVbXCJESVNNSVNTX1RPQVNUXCJdXG4gICAgICB0b2FzdElkPzogVG9hc3RlclRvYXN0W1wiaWRcIl1cbiAgICB9XG4gIHwge1xuICAgICAgdHlwZTogQWN0aW9uVHlwZVtcIlJFTU9WRV9UT0FTVFwiXVxuICAgICAgdG9hc3RJZD86IFRvYXN0ZXJUb2FzdFtcImlkXCJdXG4gICAgfVxuXG5pbnRlcmZhY2UgU3RhdGUge1xuICB0b2FzdHM6IFRvYXN0ZXJUb2FzdFtdXG59XG5cbmNvbnN0IHRvYXN0VGltZW91dHMgPSBuZXcgTWFwPHN0cmluZywgUmV0dXJuVHlwZTx0eXBlb2Ygc2V0VGltZW91dD4+KClcblxuY29uc3QgYWRkVG9SZW1vdmVRdWV1ZSA9ICh0b2FzdElkOiBzdHJpbmcpID0+IHtcbiAgaWYgKHRvYXN0VGltZW91dHMuaGFzKHRvYXN0SWQpKSB7XG4gICAgcmV0dXJuXG4gIH1cblxuICBjb25zdCB0aW1lb3V0ID0gc2V0VGltZW91dCgoKSA9PiB7XG4gICAgdG9hc3RUaW1lb3V0cy5kZWxldGUodG9hc3RJZClcbiAgICBkaXNwYXRjaCh7XG4gICAgICB0eXBlOiBcIlJFTU9WRV9UT0FTVFwiLFxuICAgICAgdG9hc3RJZDogdG9hc3RJZCxcbiAgICB9KVxuICB9LCBUT0FTVF9SRU1PVkVfREVMQVkpXG5cbiAgdG9hc3RUaW1lb3V0cy5zZXQodG9hc3RJZCwgdGltZW91dClcbn1cblxuZXhwb3J0IGNvbnN0IHJlZHVjZXIgPSAoc3RhdGU6IFN0YXRlLCBhY3Rpb246IEFjdGlvbik6IFN0YXRlID0+IHtcbiAgc3dpdGNoIChhY3Rpb24udHlwZSkge1xuICAgIGNhc2UgXCJBRERfVE9BU1RcIjpcbiAgICAgIHJldHVybiB7XG4gICAgICAgIC4uLnN0YXRlLFxuICAgICAgICB0b2FzdHM6IFthY3Rpb24udG9hc3QsIC4uLnN0YXRlLnRvYXN0c10uc2xpY2UoMCwgVE9BU1RfTElNSVQpLFxuICAgICAgfVxuXG4gICAgY2FzZSBcIlVQREFURV9UT0FTVFwiOlxuICAgICAgcmV0dXJuIHtcbiAgICAgICAgLi4uc3RhdGUsXG4gICAgICAgIHRvYXN0czogc3RhdGUudG9hc3RzLm1hcCgodCkgPT5cbiAgICAgICAgICB0LmlkID09PSBhY3Rpb24udG9hc3QuaWQgPyB7IC4uLnQsIC4uLmFjdGlvbi50b2FzdCB9IDogdFxuICAgICAgICApLFxuICAgICAgfVxuXG4gICAgY2FzZSBcIkRJU01JU1NfVE9BU1RcIjoge1xuICAgICAgY29uc3QgeyB0b2FzdElkIH0gPSBhY3Rpb25cblxuICAgICAgLy8gISBTaWRlIGVmZmVjdHMgISAtIFRoaXMgY291bGQgYmUgZXh0cmFjdGVkIGludG8gYSBkaXNtaXNzVG9hc3QoKSBhY3Rpb24sXG4gICAgICAvLyBidXQgSSdsbCBrZWVwIGl0IGhlcmUgZm9yIHNpbXBsaWNpdHlcbiAgICAgIGlmICh0b2FzdElkKSB7XG4gICAgICAgIGFkZFRvUmVtb3ZlUXVldWUodG9hc3RJZClcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHN0YXRlLnRvYXN0cy5mb3JFYWNoKCh0b2FzdCkgPT4ge1xuICAgICAgICAgIGFkZFRvUmVtb3ZlUXVldWUodG9hc3QuaWQpXG4gICAgICAgIH0pXG4gICAgICB9XG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgIC4uLnN0YXRlLFxuICAgICAgICB0b2FzdHM6IHN0YXRlLnRvYXN0cy5tYXAoKHQpID0+XG4gICAgICAgICAgdC5pZCA9PT0gdG9hc3RJZCB8fCB0b2FzdElkID09PSB1bmRlZmluZWRcbiAgICAgICAgICAgID8ge1xuICAgICAgICAgICAgICAgIC4uLnQsXG4gICAgICAgICAgICAgICAgb3BlbjogZmFsc2UsXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIDogdFxuICAgICAgICApLFxuICAgICAgfVxuICAgIH1cbiAgICBjYXNlIFwiUkVNT1ZFX1RPQVNUXCI6XG4gICAgICBpZiAoYWN0aW9uLnRvYXN0SWQgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgIC4uLnN0YXRlLFxuICAgICAgICAgIHRvYXN0czogW10sXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIHJldHVybiB7XG4gICAgICAgIC4uLnN0YXRlLFxuICAgICAgICB0b2FzdHM6IHN0YXRlLnRvYXN0cy5maWx0ZXIoKHQpID0+IHQuaWQgIT09IGFjdGlvbi50b2FzdElkKSxcbiAgICAgIH1cbiAgfVxufVxuXG5jb25zdCBsaXN0ZW5lcnM6IEFycmF5PChzdGF0ZTogU3RhdGUpID0+IHZvaWQ+ID0gW11cblxubGV0IG1lbW9yeVN0YXRlOiBTdGF0ZSA9IHsgdG9hc3RzOiBbXSB9XG5cbmZ1bmN0aW9uIGRpc3BhdGNoKGFjdGlvbjogQWN0aW9uKSB7XG4gIG1lbW9yeVN0YXRlID0gcmVkdWNlcihtZW1vcnlTdGF0ZSwgYWN0aW9uKVxuICBsaXN0ZW5lcnMuZm9yRWFjaCgobGlzdGVuZXIpID0+IHtcbiAgICBsaXN0ZW5lcihtZW1vcnlTdGF0ZSlcbiAgfSlcbn1cblxudHlwZSBUb2FzdCA9IE9taXQ8VG9hc3RlclRvYXN0LCBcImlkXCI+XG5cbmZ1bmN0aW9uIHRvYXN0KHsgLi4ucHJvcHMgfTogVG9hc3QpIHtcbiAgY29uc3QgaWQgPSBnZW5JZCgpXG5cbiAgY29uc3QgdXBkYXRlID0gKHByb3BzOiBUb2FzdGVyVG9hc3QpID0+XG4gICAgZGlzcGF0Y2goe1xuICAgICAgdHlwZTogXCJVUERBVEVfVE9BU1RcIixcbiAgICAgIHRvYXN0OiB7IC4uLnByb3BzLCBpZCB9LFxuICAgIH0pXG4gIGNvbnN0IGRpc21pc3MgPSAoKSA9PiBkaXNwYXRjaCh7IHR5cGU6IFwiRElTTUlTU19UT0FTVFwiLCB0b2FzdElkOiBpZCB9KVxuXG4gIGRpc3BhdGNoKHtcbiAgICB0eXBlOiBcIkFERF9UT0FTVFwiLFxuICAgIHRvYXN0OiB7XG4gICAgICAuLi5wcm9wcyxcbiAgICAgIGlkLFxuICAgICAgb3BlbjogdHJ1ZSxcbiAgICAgIG9uT3BlbkNoYW5nZTogKG9wZW4pID0+IHtcbiAgICAgICAgaWYgKCFvcGVuKSBkaXNtaXNzKClcbiAgICAgIH0sXG4gICAgfSxcbiAgfSlcblxuICByZXR1cm4ge1xuICAgIGlkOiBpZCxcbiAgICBkaXNtaXNzLFxuICAgIHVwZGF0ZSxcbiAgfVxufVxuXG5mdW5jdGlvbiB1c2VUb2FzdCgpIHtcbiAgY29uc3QgW3N0YXRlLCBzZXRTdGF0ZV0gPSBSZWFjdC51c2VTdGF0ZTxTdGF0ZT4obWVtb3J5U3RhdGUpXG5cbiAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICBsaXN0ZW5lcnMucHVzaChzZXRTdGF0ZSlcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgY29uc3QgaW5kZXggPSBsaXN0ZW5lcnMuaW5kZXhPZihzZXRTdGF0ZSlcbiAgICAgIGlmIChpbmRleCA+IC0xKSB7XG4gICAgICAgIGxpc3RlbmVycy5zcGxpY2UoaW5kZXgsIDEpXG4gICAgICB9XG4gICAgfVxuICB9LCBbc3RhdGVdKVxuXG4gIHJldHVybiB7XG4gICAgLi4uc3RhdGUsXG4gICAgdG9hc3QsXG4gICAgZGlzbWlzczogKHRvYXN0SWQ/OiBzdHJpbmcpID0+IGRpc3BhdGNoKHsgdHlwZTogXCJESVNNSVNTX1RPQVNUXCIsIHRvYXN0SWQgfSksXG4gIH1cbn1cblxuZXhwb3J0IHsgdXNlVG9hc3QsIHRvYXN0IH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlRPQVNUX0xJTUlUIiwiVE9BU1RfUkVNT1ZFX0RFTEFZIiwiYWN0aW9uVHlwZXMiLCJBRERfVE9BU1QiLCJVUERBVEVfVE9BU1QiLCJESVNNSVNTX1RPQVNUIiwiUkVNT1ZFX1RPQVNUIiwiY291bnQiLCJnZW5JZCIsIk51bWJlciIsIk1BWF9TQUZFX0lOVEVHRVIiLCJ0b1N0cmluZyIsInRvYXN0VGltZW91dHMiLCJNYXAiLCJhZGRUb1JlbW92ZVF1ZXVlIiwidG9hc3RJZCIsImhhcyIsInRpbWVvdXQiLCJzZXRUaW1lb3V0IiwiZGVsZXRlIiwiZGlzcGF0Y2giLCJ0eXBlIiwic2V0IiwicmVkdWNlciIsInN0YXRlIiwiYWN0aW9uIiwidG9hc3RzIiwidG9hc3QiLCJzbGljZSIsIm1hcCIsInQiLCJpZCIsImZvckVhY2giLCJ1bmRlZmluZWQiLCJvcGVuIiwiZmlsdGVyIiwibGlzdGVuZXJzIiwibWVtb3J5U3RhdGUiLCJsaXN0ZW5lciIsInByb3BzIiwidXBkYXRlIiwiZGlzbWlzcyIsIm9uT3BlbkNoYW5nZSIsInVzZVRvYXN0Iiwic2V0U3RhdGUiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInB1c2giLCJpbmRleCIsImluZGV4T2YiLCJzcGxpY2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/use-toast.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_ENDPOINTS: () => (/* binding */ API_ENDPOINTS),\n/* harmony export */   ApiClient: () => (/* binding */ ApiClient),\n/* harmony export */   TokenManager: () => (/* binding */ TokenManager),\n/* harmony export */   apiClient: () => (/* binding */ apiClient)\n/* harmony export */ });\n// API配置和服务层\n// 兼容 Cloudflare Pages 部署\n// API配置\nconst API_CONFIG = {\n    // 生产环境使用你的 Cloudflare Worker 域名\n    BASE_URL: \"http://localhost:3001\" || 0,\n    // 备用API配置 - 支持多个备用API（逗号分隔）\n    BACKUP_URL: \"http://localhost:3001\" || 0,\n    BACKUP_URLS:  true ? \"http://localhost:3001\".split(',').map((url)=>url.trim()).filter((url)=>url.length > 0) : 0,\n    ENABLE_BACKUP: \"true\" === 'true',\n    TIMEOUT: 30000\n};\n// API端点定义\nconst API_ENDPOINTS = {\n    // 认证相关\n    AUTH: {\n        LOGIN: '/api/auth/login',\n        REGISTER: '/api/auth/register',\n        REFRESH: '/api/auth/refresh',\n        SEND_VERIFICATION: '/api/auth/send-verification',\n        VERIFY_EMAIL: '/api/auth/verify-email',\n        CHANGE_PASSWORD: '/api/auth/change-password',\n        FORGOT_PASSWORD: '/api/auth/forgot-password',\n        RESET_PASSWORD: '/api/auth/reset-password'\n    },\n    // TTS相关\n    TTS: {\n        GENERATE: '/api/tts/generate',\n        STATUS: '/api/tts/status',\n        STREAM: '/api/tts/stream',\n        DOWNLOAD: '/api/tts/download'\n    },\n    // 用户相关\n    USER: {\n        QUOTA: '/api/user/quota'\n    },\n    // 卡密相关\n    CARD: {\n        USE: '/api/card/use'\n    },\n    // 自动标注相关\n    AUTO_TAG: {\n        PROCESS: '/api/auto-tag/process',\n        STATUS: '/api/auto-tag/status',\n        ADMIN_STATS: '/api/auto-tag/admin/stats'\n    }\n};\n// Token管理\nclass TokenManager {\n    static getAccessToken() {\n        if (false) {}\n        return localStorage.getItem(this.ACCESS_TOKEN_KEY);\n    }\n    static getRefreshToken() {\n        if (false) {}\n        return localStorage.getItem(this.REFRESH_TOKEN_KEY);\n    }\n    static getUserEmail() {\n        if (false) {}\n        return localStorage.getItem(this.USER_EMAIL_KEY);\n    }\n    static setTokens(accessToken, refreshToken, email) {\n        if (false) {}\n        localStorage.setItem(this.ACCESS_TOKEN_KEY, accessToken);\n        localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);\n        if (email) {\n            localStorage.setItem(this.USER_EMAIL_KEY, email);\n        }\n        // 保持兼容性\n        localStorage.setItem('isLoggedIn', 'true');\n    }\n    static clearTokens() {\n        if (false) {}\n        localStorage.removeItem(this.ACCESS_TOKEN_KEY);\n        localStorage.removeItem(this.REFRESH_TOKEN_KEY);\n        localStorage.removeItem(this.USER_EMAIL_KEY);\n        // 保持兼容性\n        localStorage.removeItem('isLoggedIn');\n    }\n    static isLoggedIn() {\n        return !!this.getAccessToken();\n    }\n}\nTokenManager.ACCESS_TOKEN_KEY = 'access_token';\nTokenManager.REFRESH_TOKEN_KEY = 'refresh_token';\nTokenManager.USER_EMAIL_KEY = 'userEmail';\n// HTTP客户端类\nclass ApiClient {\n    // 获取当前可用的API URL（主要用于外部访问）\n    getCurrentApiUrl() {\n        let useBackup = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false, backupIndex = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        if (useBackup && this.enableBackup) {\n            // 优先使用多个备用API配置\n            if (this.backupURLs.length > 0 && backupIndex >= 0 && backupIndex < this.backupURLs.length) {\n                return this.backupURLs[backupIndex];\n            }\n            // 向后兼容：使用单个备用API配置\n            if (this.backupURL) {\n                return this.backupURL;\n            }\n        }\n        return this.baseURL;\n    }\n    // 检查备用API是否可用\n    isBackupApiAvailable() {\n        return this.enableBackup && (this.backupURLs.length > 0 || !!this.backupURL);\n    }\n    // 获取备用API数量\n    getBackupApiCount() {\n        if (!this.enableBackup) return 0;\n        return this.backupURLs.length > 0 ? this.backupURLs.length : this.backupURL ? 1 : 0;\n    }\n    // 获取指定索引的备用API URL\n    getBackupApiUrl(index) {\n        if (!this.enableBackup) return null;\n        // 优先使用多个备用API配置\n        if (this.backupURLs.length > 0) {\n            return index >= 0 && index < this.backupURLs.length ? this.backupURLs[index] : null;\n        }\n        // 向后兼容：使用单个备用API配置\n        return index === 0 && this.backupURL ? this.backupURL : null;\n    }\n    // 创建请求头\n    createHeaders() {\n        let includeAuth = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        const headers = {\n            'Content-Type': 'application/json'\n        };\n        if (includeAuth) {\n            const token = TokenManager.getAccessToken();\n            if (token) {\n                headers['Authorization'] = \"Bearer \".concat(token);\n            }\n        }\n        return headers;\n    }\n    // 处理响应\n    async handleResponse(response) {\n        if (!response.ok) {\n            let errorMessage = \"HTTP \".concat(response.status, \": \").concat(response.statusText);\n            let errorCode = null;\n            try {\n                const errorData = await response.json();\n                errorMessage = errorData.error || errorData.message || errorMessage;\n                errorCode = errorData.code || null // 【新增】提取错误码\n                ;\n            } catch (e) {\n            // 如果无法解析JSON，使用默认错误消息\n            }\n            // 【新增】创建带有错误码的自定义错误对象\n            const error = new Error(errorMessage);\n            if (errorCode) {\n                error.code = errorCode;\n            }\n            throw error;\n        }\n        return await response.json();\n    }\n    // 通用请求方法\n    async request(endpoint) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, includeAuth = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        const url = \"\".concat(this.baseURL).concat(endpoint);\n        const headers = this.createHeaders(includeAuth);\n        const config = {\n            ...options,\n            headers: {\n                ...headers,\n                ...options.headers\n            }\n        };\n        // 创建AbortController用于超时控制\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), this.timeout);\n        try {\n            const response = await fetch(url, {\n                ...config,\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            return await this.handleResponse(response);\n        } catch (error) {\n            clearTimeout(timeoutId);\n            if (error instanceof Error) {\n                if (error.name === 'AbortError') {\n                    throw new Error('请求超时，请检查网络连接');\n                }\n                throw error;\n            }\n            throw new Error('网络请求失败');\n        }\n    }\n    // GET请求\n    async get(endpoint) {\n        let includeAuth = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        return this.request(endpoint, {\n            method: 'GET'\n        }, includeAuth);\n    }\n    // POST请求\n    async post(endpoint, data) {\n        let includeAuth = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        return this.request(endpoint, {\n            method: 'POST',\n            body: data ? JSON.stringify(data) : undefined\n        }, includeAuth);\n    }\n    // PUT请求\n    async put(endpoint, data) {\n        let includeAuth = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n        return this.request(endpoint, {\n            method: 'PUT',\n            body: data ? JSON.stringify(data) : undefined\n        }, includeAuth);\n    }\n    // DELETE请求\n    async delete(endpoint) {\n        let includeAuth = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        return this.request(endpoint, {\n            method: 'DELETE'\n        }, includeAuth);\n    }\n    constructor(){\n        this.baseURL = API_CONFIG.BASE_URL;\n        this.backupURL = API_CONFIG.BACKUP_URL;\n        this.backupURLs = API_CONFIG.BACKUP_URLS;\n        this.enableBackup = API_CONFIG.ENABLE_BACKUP;\n        this.timeout = API_CONFIG.TIMEOUT;\n    }\n}\n// 创建全局API客户端实例\nconst apiClient = new ApiClient();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/auth-service.ts":
/*!*****************************!*\
  !*** ./lib/auth-service.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: () => (/* binding */ AuthService),\n/* harmony export */   AutoTagService: () => (/* binding */ AutoTagService),\n/* harmony export */   CardService: () => (/* binding */ CardService),\n/* harmony export */   TTSService: () => (/* binding */ TTSService),\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   autoTagService: () => (/* binding */ autoTagService),\n/* harmony export */   cardService: () => (/* binding */ cardService),\n/* harmony export */   ttsService: () => (/* binding */ ttsService)\n/* harmony export */ });\n/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api */ \"(app-pages-browser)/./lib/api.ts\");\n// 认证服务\n// 专门处理认证相关的API调用\n\nclass AuthService {\n    // 登录\n    static async login(credentials) {\n        try {\n            const response = await _api__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_api__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.AUTH.LOGIN, credentials);\n            // 存储token\n            _api__WEBPACK_IMPORTED_MODULE_0__.TokenManager.setTokens(response.access_token, response.refresh_token, credentials.username);\n            return response;\n        } catch (error) {\n            console.error('Login error:', error);\n            throw error;\n        }\n    }\n    // 传统注册（保持兼容性）\n    static async register(userData) {\n        try {\n            const response = await _api__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_api__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.AUTH.REGISTER, userData);\n            // 存储token\n            _api__WEBPACK_IMPORTED_MODULE_0__.TokenManager.setTokens(response.access_token, response.refresh_token, userData.username);\n            return response;\n        } catch (error) {\n            console.error('Register error:', error);\n            throw error;\n        }\n    }\n    // 发送邮箱验证码\n    static async sendVerificationCode(data) {\n        try {\n            const response = await _api__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_api__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.AUTH.SEND_VERIFICATION, data);\n            return response;\n        } catch (error) {\n            console.error('Send verification error:', error);\n            throw error;\n        }\n    }\n    // 验证邮箱并完成注册\n    static async verifyEmailAndRegister(data) {\n        try {\n            const response = await _api__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_api__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.AUTH.VERIFY_EMAIL, data);\n            // 存储token\n            _api__WEBPACK_IMPORTED_MODULE_0__.TokenManager.setTokens(response.access_token, response.refresh_token, data.email);\n            return response;\n        } catch (error) {\n            console.error('Verify email error:', error);\n            throw error;\n        }\n    }\n    // 刷新token\n    static async refreshToken() {\n        try {\n            const refreshToken = _api__WEBPACK_IMPORTED_MODULE_0__.TokenManager.getRefreshToken();\n            if (!refreshToken) {\n                throw new Error('No refresh token available');\n            }\n            const response = await _api__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_api__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.AUTH.REFRESH, {\n                refresh_token: refreshToken\n            });\n            // 更新token\n            _api__WEBPACK_IMPORTED_MODULE_0__.TokenManager.setTokens(response.access_token, response.refresh_token);\n            return response;\n        } catch (error) {\n            console.error('Refresh token error:', error);\n            // 如果刷新失败，清除所有token\n            _api__WEBPACK_IMPORTED_MODULE_0__.TokenManager.clearTokens();\n            throw error;\n        }\n    }\n    // 登出\n    static async logout() {\n        try {\n            // 清除本地token\n            _api__WEBPACK_IMPORTED_MODULE_0__.TokenManager.clearTokens();\n        // 可以在这里添加服务端登出逻辑\n        // await apiClient.post(API_ENDPOINTS.AUTH.LOGOUT, {}, true)\n        } catch (error) {\n            console.error('Logout error:', error);\n            // 即使服务端登出失败，也要清除本地token\n            _api__WEBPACK_IMPORTED_MODULE_0__.TokenManager.clearTokens();\n        }\n    }\n    // 获取用户配额信息\n    static async getUserQuota() {\n        try {\n            return await this.withTokenRefresh(async ()=>{\n                return await _api__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_api__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.USER.QUOTA, true // 需要认证\n                );\n            });\n        } catch (error) {\n            console.error('Get user quota error:', error);\n            throw error;\n        }\n    }\n    // 修改密码\n    static async changePassword(data) {\n        try {\n            return await this.withTokenRefresh(async ()=>{\n                return await _api__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_api__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.AUTH.CHANGE_PASSWORD, data, true // 需要认证\n                );\n            });\n        } catch (error) {\n            console.error('Change password error:', error);\n            throw error;\n        }\n    }\n    // 忘记密码 - 发送重置验证码\n    static async forgotPassword(data) {\n        try {\n            const response = await _api__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_api__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.AUTH.FORGOT_PASSWORD, data);\n            return response;\n        } catch (error) {\n            console.error('Forgot password error:', error);\n            throw error;\n        }\n    }\n    // 重置密码\n    static async resetPassword(data) {\n        try {\n            const response = await _api__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_api__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.AUTH.RESET_PASSWORD, data);\n            return response;\n        } catch (error) {\n            console.error('Reset password error:', error);\n            throw error;\n        }\n    }\n    // 检查登录状态\n    static isLoggedIn() {\n        return _api__WEBPACK_IMPORTED_MODULE_0__.TokenManager.isLoggedIn();\n    }\n    // 获取当前用户邮箱\n    static getCurrentUserEmail() {\n        return _api__WEBPACK_IMPORTED_MODULE_0__.TokenManager.getUserEmail();\n    }\n    // 【新增】判断是否为认证错误的辅助函数\n    static isAuthError(error) {\n        // 优先检查错误码（更可靠）\n        if (error.code) {\n            return error.code === 'TOKEN_EXPIRED' || error.code === 'TOKEN_INVALID' || error.code === 'TOKEN_TYPE_INVALID' || error.code === 'NO_TOKEN';\n        }\n        // 兼容旧版：检查错误消息\n        if (error.message) {\n            return error.message.includes('401') || error.message.toLowerCase().includes('token') || error.message.toLowerCase().includes('expired') || error.message.includes('登录') || error.message.includes('unauthorized');\n        }\n        return false;\n    }\n    // 自动刷新token的包装器\n    static async withTokenRefresh(apiCall) {\n        try {\n            return await apiCall();\n        } catch (error) {\n            // 【关键修改】使用新的认证错误判断逻辑\n            if (this.isAuthError(error)) {\n                // Token可能过期，尝试刷新\n                try {\n                    await this.refreshToken();\n                    // 重试原始请求\n                    return await apiCall();\n                } catch (refreshError) {\n                    // 刷新失败，清理本地数据\n                    this.logout();\n                    // 【修复】创建一个特殊的认证失败错误，让调用方处理UI和跳转\n                    const authFailedError = new Error('Authentication failed - refresh token expired');\n                    authFailedError.code = 'REFRESH_TOKEN_EXPIRED';\n                    authFailedError.shouldRedirect = true;\n                    throw authFailedError;\n                }\n            }\n            throw error;\n        }\n    }\n}\n// 卡密服务\nclass CardService {\n    // 使用卡密充值\n    static async useCard(code) {\n        try {\n            return await AuthService.withTokenRefresh(async ()=>{\n                return await _api__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_api__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.CARD.USE, {\n                    code\n                }, true // 需要认证\n                );\n            });\n        } catch (error) {\n            console.error('Use card error:', error);\n            throw error;\n        }\n    }\n}\n// TTS服务\nclass TTSService {\n    // 原有的同步生成语音方法（保持向后兼容）\n    static async generateSpeech(data) {\n        try {\n            return await AuthService.withTokenRefresh(async ()=>{\n                const response = await fetch(\"\".concat(_api__WEBPACK_IMPORTED_MODULE_0__.apiClient.baseURL).concat(_api__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.TTS.GENERATE), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'Authorization': \"Bearer \".concat(_api__WEBPACK_IMPORTED_MODULE_0__.TokenManager.getAccessToken())\n                    },\n                    body: JSON.stringify(data)\n                });\n                if (!response.ok) {\n                    const errorData = await response.json().catch(()=>({}));\n                    const error = new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n                    // 传递错误类型信息\n                    if (errorData.type) {\n                        error.type = errorData.type;\n                    }\n                    throw error;\n                }\n                return await response.arrayBuffer();\n            });\n        } catch (error) {\n            console.error('Generate speech error:', error);\n            throw error;\n        }\n    }\n    // 新的异步任务启动方法\n    static async startAsyncGeneration(data) {\n        try {\n            return await AuthService.withTokenRefresh(async ()=>{\n                const response = await fetch(\"\".concat(_api__WEBPACK_IMPORTED_MODULE_0__.apiClient.baseURL).concat(_api__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.TTS.GENERATE), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'Authorization': \"Bearer \".concat(_api__WEBPACK_IMPORTED_MODULE_0__.TokenManager.getAccessToken())\n                    },\n                    body: JSON.stringify(data)\n                });\n                if (!response.ok) {\n                    const errorData = await response.json().catch(()=>({}));\n                    const error = new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n                    // 传递错误类型信息\n                    if (errorData.type) {\n                        error.type = errorData.type;\n                    }\n                    throw error;\n                }\n                return await response.json();\n            });\n        } catch (error) {\n            console.error('Start async generation error:', error);\n            throw error;\n        }\n    }\n    // 检查任务状态\n    static async checkTaskStatus(taskId) {\n        try {\n            return await AuthService.withTokenRefresh(async ()=>{\n                const response = await fetch(\"\".concat(_api__WEBPACK_IMPORTED_MODULE_0__.apiClient.baseURL).concat(_api__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.TTS.STATUS, \"/\").concat(taskId), {\n                    method: 'GET',\n                    headers: {\n                        'Authorization': \"Bearer \".concat(_api__WEBPACK_IMPORTED_MODULE_0__.TokenManager.getAccessToken())\n                    }\n                });\n                if (!response.ok) {\n                    const errorData = await response.json().catch(()=>({}));\n                    throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n                }\n                return await response.json();\n            });\n        } catch (error) {\n            console.error('Check task status error:', error);\n            throw error;\n        }\n    }\n    // 下载完成的音频 - Worker代理方式（备用）\n    static async downloadAudio(taskId) {\n        try {\n            return await AuthService.withTokenRefresh(async ()=>{\n                const response = await fetch(\"\".concat(_api__WEBPACK_IMPORTED_MODULE_0__.apiClient.baseURL).concat(_api__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.TTS.DOWNLOAD, \"/\").concat(taskId), {\n                    method: 'GET',\n                    headers: {\n                        'Authorization': \"Bearer \".concat(_api__WEBPACK_IMPORTED_MODULE_0__.TokenManager.getAccessToken())\n                    }\n                });\n                if (!response.ok) {\n                    const errorData = await response.json().catch(()=>({}));\n                    throw new Error(errorData.error || \"HTTP \".concat(response.status, \": \").concat(response.statusText));\n                }\n                return await response.arrayBuffer();\n            });\n        } catch (error) {\n            console.error('Download audio error:', error);\n            throw error;\n        }\n    }\n    // R2直链下载方式（优化后）\n    static async downloadFromDirectUrl(directUrl) {\n        try {\n            // 检查是否为开发环境且存在CORS问题\n            const isDevelopment = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';\n            let fetchOptions = {\n                method: 'GET'\n            };\n            // 开发环境CORS处理\n            if (isDevelopment) {\n                // 尝试直接访问，如果CORS失败则通过代理\n                try {\n                    await fetch(directUrl, {\n                        method: 'HEAD',\n                        mode: 'cors'\n                    });\n                } catch (corsError) {\n                    fetchOptions.mode = 'no-cors';\n                    // 如果no-cors也不行，则抛出错误让其回退到Worker代理\n                    if (corsError instanceof TypeError && corsError.message.includes('CORS')) {\n                        throw new Error(\"CORS_ERROR: \".concat(corsError.message));\n                    }\n                }\n            }\n            const response = await fetch(directUrl, fetchOptions);\n            if (!response.ok) {\n                throw new Error(\"R2 direct download failed: HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            const arrayBuffer = await response.arrayBuffer();\n            return arrayBuffer;\n        } catch (error) {\n            // 如果是CORS错误，提供更详细的错误信息\n            if (error instanceof Error && error.message.includes('CORS')) {\n                throw new Error(\"R2_CORS_ERROR: CORS configuration needed for r2-assets.aispeak.top. \".concat(error.message));\n            }\n            throw error;\n        }\n    }\n    // 智能轮询任务状态直到完成\n    static async pollTaskUntilComplete(taskId, onProgress) {\n        let maxAttempts = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 60, initialDelay = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 2000;\n        // 检查是否已经在下载中\n        if (this.downloadCache.has(taskId)) {\n            return await this.downloadCache.get(taskId);\n        }\n        // 创建下载Promise并缓存\n        const downloadPromise = this.performPolling(taskId, onProgress, maxAttempts, initialDelay);\n        this.downloadCache.set(taskId, downloadPromise);\n        try {\n            const result = await downloadPromise;\n            // 下载完成后清理缓存\n            this.downloadCache.delete(taskId);\n            return result;\n        } catch (error) {\n            // 下载失败也要清理缓存\n            this.downloadCache.delete(taskId);\n            throw error;\n        }\n    }\n    // 实际的轮询逻辑\n    static async performPolling(taskId, onProgress) {\n        let maxAttempts = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 60, initialDelay = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 2000;\n        let attempts = 0;\n        let delay = initialDelay;\n        while(attempts < maxAttempts){\n            try {\n                const status = await this.checkTaskStatus(taskId);\n                // 调用进度回调\n                if (onProgress) {\n                    onProgress(status);\n                }\n                if (status.status === 'complete') {\n                    // 任务完成，智能选择下载方式\n                    // 优先使用R2直链下载\n                    if (status.audioUrl && status.audioUrl.includes('r2-assets.aispeak.top')) {\n                        try {\n                            return await this.downloadFromDirectUrl(status.audioUrl);\n                        } catch (r2Error) {\n                            // R2直链失败，回退到Worker代理下载\n                            return await this.downloadAudio(taskId);\n                        }\n                    } else {\n                        // 没有R2直链或不是R2直链，使用Worker代理下载\n                        return await this.downloadAudio(taskId);\n                    }\n                } else if (status.status === 'failed') {\n                    // 任务失败\n                    throw new Error(status.error || 'Task failed');\n                }\n                // 任务仍在处理中，等待后重试\n                await new Promise((resolve)=>setTimeout(resolve, delay));\n                // 指数退避：延迟时间逐渐增加，但不超过10秒\n                delay = Math.min(delay * 1.2, 10000);\n                attempts++;\n            } catch (error) {\n                console.error(\"Polling attempt \".concat(attempts + 1, \" failed:\"), error);\n                attempts++;\n                // 如果是网络错误，稍等后重试\n                if (attempts < maxAttempts) {\n                    await new Promise((resolve)=>setTimeout(resolve, delay));\n                    delay = Math.min(delay * 1.5, 10000);\n                } else {\n                    throw error;\n                }\n            }\n        }\n        throw new Error('Task polling timeout - maximum attempts reached');\n    }\n}\n// 防重复下载缓存\nTTSService.downloadCache = new Map();\n// 自动标注服务\nclass AutoTagService {\n    // 处理自动标注请求\n    static async processText(text) {\n        let language = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'auto';\n        try {\n            return await AuthService.withTokenRefresh(async ()=>{\n                return await _api__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(_api__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.AUTO_TAG.PROCESS, {\n                    text,\n                    language\n                }, true // 需要认证\n                );\n            });\n        } catch (error) {\n            console.error('Auto tag process error:', error);\n            throw error;\n        }\n    }\n    // 获取使用状态\n    static async getStatus() {\n        try {\n            return await AuthService.withTokenRefresh(async ()=>{\n                return await _api__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(_api__WEBPACK_IMPORTED_MODULE_0__.API_ENDPOINTS.AUTO_TAG.STATUS, true // 需要认证\n                );\n            });\n        } catch (error) {\n            console.error('Auto tag status error:', error);\n            throw error;\n        }\n    }\n}\n// 导出便捷方法\nconst auth = AuthService;\nconst cardService = CardService;\nconst ttsService = TTSService;\nconst autoTagService = AutoTagService;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/auth-service.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/error-utils.ts":
/*!****************************!*\
  !*** ./lib/error-utils.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AUTH_ERROR_CODES: () => (/* binding */ AUTH_ERROR_CODES),\n/* harmony export */   containsSensitiveInfo: () => (/* binding */ containsSensitiveInfo),\n/* harmony export */   getAuthErrorMessage: () => (/* binding */ getAuthErrorMessage),\n/* harmony export */   getErrorUIState: () => (/* binding */ getErrorUIState),\n/* harmony export */   getSafeUserFriendlyMessage: () => (/* binding */ getSafeUserFriendlyMessage),\n/* harmony export */   getUserFriendlyMessage: () => (/* binding */ getUserFriendlyMessage),\n/* harmony export */   handleAuthError: () => (/* binding */ handleAuthError),\n/* harmony export */   isAuthError: () => (/* binding */ isAuthError),\n/* harmony export */   isTokenExpiredError: () => (/* binding */ isTokenExpiredError),\n/* harmony export */   sanitizeErrorMessage: () => (/* binding */ sanitizeErrorMessage)\n/* harmony export */ });\n/**\n * 前端错误处理工具函数\n * 配合后端的结构化错误响应，提供统一的错误识别和处理\n * 包含前端敏感信息过滤功能，作为最后一道防线\n */ // 认证相关错误码\nconst AUTH_ERROR_CODES = {\n    TOKEN_EXPIRED: 'TOKEN_EXPIRED',\n    TOKEN_INVALID: 'TOKEN_INVALID',\n    TOKEN_TYPE_INVALID: 'TOKEN_TYPE_INVALID',\n    NO_TOKEN: 'NO_TOKEN',\n    AUTH_ERROR: 'AUTH_ERROR',\n    REFRESH_TOKEN_EXPIRED: 'REFRESH_TOKEN_EXPIRED'\n};\n// 前端敏感信息检测模式\nconst FRONTEND_SENSITIVE_PATTERNS = [\n    // 文件路径和系统信息\n    /[A-Za-z]:\\\\[\\w\\\\.-]+/g,\n    /\\/[\\w\\/.-]+\\.(js|ts|json|sql|env)/g,\n    /node_modules/gi,\n    /at\\s+[\\w.]+\\s+\\(/g,\n    // 网络和API信息\n    /https?:\\/\\/[\\w.-]+\\/[\\w\\/.-]*/g,\n    /localhost:\\d+/g,\n    /\\b(?:\\d{1,3}\\.){3}\\d{1,3}:\\d+\\b/g,\n    // 数据库和系统错误\n    /table\\s+[\"']?\\w+[\"']?/gi,\n    /column\\s+[\"']?\\w+[\"']?/gi,\n    /constraint\\s+[\"']?\\w+[\"']?/gi,\n    /error:\\s*\\w+error/gi,\n    /errno\\s*:\\s*\\d+/gi,\n    // 敏感关键词\n    /api[_-]?key/gi,\n    /secret/gi,\n    /password/gi\n];\n// 用户友好的错误消息映射\nconst FRONTEND_SAFE_MESSAGES = {\n    // 网络相关\n    'network': '网络连接异常，请检查网络后重试',\n    'timeout': '请求超时，请稍后重试',\n    'fetch': '网络请求失败，请稍后重试',\n    // 系统相关\n    'internal': '系统暂时繁忙，请稍后再试',\n    'server': '服务器暂时不可用，请稍后再试',\n    'database': '数据处理异常，请稍后重试',\n    // 认证相关\n    'auth': '认证失败，请重新登录',\n    'token': '登录会话已过期，请重新登录',\n    'unauthorized': '未授权访问，请先登录',\n    // 通用错误\n    'unknown': '发生未知错误，请稍后重试',\n    'default': '操作失败，请稍后重试'\n};\n/**\n * 检测错误消息中的敏感信息（前端最后防线）\n * @param message 错误消息\n * @returns 是否包含敏感信息\n */ function containsSensitiveInfo(message) {\n    if (!message || typeof message !== 'string') {\n        return false;\n    }\n    return FRONTEND_SENSITIVE_PATTERNS.some((pattern)=>pattern.test(message));\n}\n/**\n * 清理错误消息中的敏感信息\n * @param message 原始错误消息\n * @returns 清理后的错误消息\n */ function sanitizeErrorMessage(message) {\n    if (!message || typeof message !== 'string') {\n        return FRONTEND_SAFE_MESSAGES.default;\n    }\n    // 如果包含敏感信息，返回通用消息\n    if (containsSensitiveInfo(message)) {\n        // 根据消息内容返回相应的安全消息\n        const lowerMessage = message.toLowerCase();\n        if (lowerMessage.includes('network') || lowerMessage.includes('fetch')) {\n            return FRONTEND_SAFE_MESSAGES.network;\n        } else if (lowerMessage.includes('timeout')) {\n            return FRONTEND_SAFE_MESSAGES.timeout;\n        } else if (lowerMessage.includes('auth') || lowerMessage.includes('token')) {\n            return FRONTEND_SAFE_MESSAGES.auth;\n        } else if (lowerMessage.includes('server') || lowerMessage.includes('internal')) {\n            return FRONTEND_SAFE_MESSAGES.server;\n        } else if (lowerMessage.includes('database')) {\n            return FRONTEND_SAFE_MESSAGES.database;\n        } else {\n            return FRONTEND_SAFE_MESSAGES.unknown;\n        }\n    }\n    // 没有敏感信息，返回原始消息\n    return message;\n}\n/**\n * 判断是否为认证相关错误\n * @param error 错误对象\n * @returns 是否为认证错误\n */ function isAuthError(error) {\n    // 优先检查错误码（最可靠）\n    if (error === null || error === void 0 ? void 0 : error.code) {\n        return Object.values(AUTH_ERROR_CODES).includes(error.code);\n    }\n    // 兼容性检查：检查错误消息\n    if (error === null || error === void 0 ? void 0 : error.message) {\n        const message = error.message.toLowerCase();\n        return message.includes('token') || message.includes('expired') || message.includes('unauthorized') || message.includes('401') || message.includes('登录') || message.includes('refresh');\n    }\n    return false;\n}\n/**\n * 判断是否为token过期错误\n * @param error 错误对象\n * @returns 是否为token过期错误\n */ function isTokenExpiredError(error) {\n    // 优先检查错误码\n    if ((error === null || error === void 0 ? void 0 : error.code) === AUTH_ERROR_CODES.TOKEN_EXPIRED) {\n        return true;\n    }\n    // 兼容性检查：检查错误消息\n    if (error === null || error === void 0 ? void 0 : error.message) {\n        const message = error.message.toLowerCase();\n        return message.includes('token expired') || message.includes('expired') || message.includes('过期');\n    }\n    return false;\n}\n/**\n * 生成安全的用户友好错误消息（前端最后防线）\n * @param error 错误对象\n * @returns 安全的用户友好错误消息\n */ function getSafeUserFriendlyMessage(error) {\n    let message = '';\n    // 提取错误消息\n    if (error === null || error === void 0 ? void 0 : error.message) {\n        message = error.message;\n    } else if (error === null || error === void 0 ? void 0 : error.error) {\n        message = error.error;\n    } else if (typeof error === 'string') {\n        message = error;\n    } else {\n        message = '操作失败，请稍后重试';\n    }\n    // 如果是认证错误，返回认证相关消息\n    if (isAuthError(error)) {\n        return '登录会话已过期，请重新登录';\n    }\n    // 清理敏感信息\n    return sanitizeErrorMessage(message);\n}\n/**\n * 获取用户友好的错误消息\n * @param error 错误对象\n * @returns 用户友好的错误消息\n */ function getAuthErrorMessage(error) {\n    if (isAuthError(error)) {\n        // 【修复】检查错误对象上的shouldRedirect属性，默认为true\n        const shouldRedirect = (error === null || error === void 0 ? void 0 : error.shouldRedirect) !== undefined ? error.shouldRedirect : true;\n        return {\n            title: \"认证失败\",\n            description: \"会话已过期，正在跳转到登录页面...\",\n            shouldRedirect: shouldRedirect\n        };\n    }\n    // 业务错误或网络错误 - 使用安全消息\n    return {\n        title: \"操作失败\",\n        description: getSafeUserFriendlyMessage(error),\n        shouldRedirect: false\n    };\n}\n/**\n * 统一的认证错误处理函数\n * @param error 错误对象\n * @param onAuthError 认证错误回调（可选）\n * @returns 处理结果\n */ function handleAuthError(error, onAuthError) {\n    const isAuth = isAuthError(error);\n    if (isAuth && onAuthError) {\n        onAuthError();\n    }\n    const errorInfo = getAuthErrorMessage(error);\n    return {\n        isAuthError: isAuth,\n        message: errorInfo.description,\n        shouldRedirect: errorInfo.shouldRedirect\n    };\n}\n/**\n * 为页面组件提供的错误处理hook辅助函数\n * @param error 错误对象\n * @returns UI状态更新信息\n */ function getErrorUIState(error) {\n    var _error_message;\n    if (isAuthError(error)) {\n        return {\n            statusDisplay: \"请重新登录\",\n            toastTitle: \"认证失败\",\n            toastDescription: \"会话已过期，正在跳转到登录页面...\",\n            variant: 'destructive'\n        };\n    }\n    // 检查是否为业务错误（如卡密无效等）\n    if (error === null || error === void 0 ? void 0 : (_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('卡密')) {\n        return {\n            statusDisplay: \"充值失败\",\n            toastTitle: \"充值失败\",\n            toastDescription: \"卡密无效或已使用，请检查后重试\",\n            variant: 'destructive'\n        };\n    }\n    // 网络或其他错误 - 使用安全消息\n    return {\n        statusDisplay: \"获取失败\",\n        toastTitle: \"操作失败\",\n        toastDescription: getSafeUserFriendlyMessage(error),\n        variant: 'destructive'\n    };\n}\n/**\n * 生成用户友好的错误消息（向后兼容版本）\n * @param error 错误对象\n * @returns 用户友好的错误消息\n */ function getUserFriendlyMessage(error) {\n    // 使用安全版本\n    return getSafeUserFriendlyMessage(error);\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/error-utils.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn() {\n    for(var _len = arguments.length, inputs = new Array(_len), _key = 0; _key < _len; _key++){\n        inputs[_key] = arguments[_key];\n    }\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2xpYi91dGlscy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNEM7QUFDSjtBQUVqQyxTQUFTRTtJQUFHO1FBQUdDLE9BQUgsdUJBQXVCOztJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJEOlxcbXlhaXR0c1xcZnJvbnRlbmRcXGxpYlxcdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2xzeCwgdHlwZSBDbGFzc1ZhbHVlIH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/utils.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-compose-refs/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeRefs: () => (/* binding */ composeRefs),\n/* harmony export */   useComposedRefs: () => (/* binding */ useComposedRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n// packages/react/compose-refs/src/composeRefs.tsx\n\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@radix-ui/react-slot/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@radix-ui/react-slot/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Slot: () => (/* binding */ Slot),\n/* harmony export */   Slottable: () => (/* binding */ Slottable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(app-pages-browser)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n// packages/react/slot/src/Slot.tsx\n\n\n\nvar Slot = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  const childrenArray = react__WEBPACK_IMPORTED_MODULE_0__.Children.toArray(children);\n  const slottable = childrenArray.find(isSlottable);\n  if (slottable) {\n    const newElement = slottable.props.children;\n    const newChildren = childrenArray.map((child) => {\n      if (child === slottable) {\n        if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(newElement) > 1) return react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null);\n        return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? newElement.props.children : null;\n      } else {\n        return child;\n      }\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children: react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(newElement, void 0, newChildren) : null });\n  }\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children });\n});\nSlot.displayName = \"Slot\";\nvar SlotClone = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  if (react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)) {\n    const childrenRef = getElementRef(children);\n    return react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, {\n      ...mergeProps(slotProps, children.props),\n      // @ts-ignore\n      ref: forwardedRef ? (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.composeRefs)(forwardedRef, childrenRef) : childrenRef\n    });\n  }\n  return react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null) : null;\n});\nSlotClone.displayName = \"SlotClone\";\nvar Slottable = ({ children }) => {\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, { children });\n};\nfunction isSlottable(child) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(child) && child.type === Slottable;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          childPropValue(...args);\n          slotPropValue(...args);\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nvar Root = Slot;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@radix-ui/react-slot/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/class-variance-authority/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/class-variance-authority/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cva: () => (/* binding */ cva),\n/* harmony export */   cx: () => (/* binding */ cx)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\");\n/**\n * Copyright 2022 Joe Bell. All rights reserved.\n *\n * This file is licensed to you under the Apache License, Version 2.0\n * (the \"License\"); you may not use this file except in compliance with the\n * License. You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS, WITHOUT\n * WARRANTIES OR REPRESENTATIONS OF ANY KIND, either express or implied. See the\n * License for the specific language governing permissions and limitations under\n * the License.\n */ \nconst falsyToString = (value)=>typeof value === \"boolean\" ? `${value}` : value === 0 ? \"0\" : value;\nconst cx = clsx__WEBPACK_IMPORTED_MODULE_0__.clsx;\nconst cva = (base, config)=>(props)=>{\n        var _config_compoundVariants;\n        if ((config === null || config === void 0 ? void 0 : config.variants) == null) return cx(base, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n        const { variants, defaultVariants } = config;\n        const getVariantClassNames = Object.keys(variants).map((variant)=>{\n            const variantProp = props === null || props === void 0 ? void 0 : props[variant];\n            const defaultVariantProp = defaultVariants === null || defaultVariants === void 0 ? void 0 : defaultVariants[variant];\n            if (variantProp === null) return null;\n            const variantKey = falsyToString(variantProp) || falsyToString(defaultVariantProp);\n            return variants[variant][variantKey];\n        });\n        const propsWithoutUndefined = props && Object.entries(props).reduce((acc, param)=>{\n            let [key, value] = param;\n            if (value === undefined) {\n                return acc;\n            }\n            acc[key] = value;\n            return acc;\n        }, {});\n        const getCompoundVariantClassNames = config === null || config === void 0 ? void 0 : (_config_compoundVariants = config.compoundVariants) === null || _config_compoundVariants === void 0 ? void 0 : _config_compoundVariants.reduce((acc, param)=>{\n            let { class: cvClass, className: cvClassName, ...compoundVariantOptions } = param;\n            return Object.entries(compoundVariantOptions).every((param)=>{\n                let [key, value] = param;\n                return Array.isArray(value) ? value.includes({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                }[key]) : ({\n                    ...defaultVariants,\n                    ...propsWithoutUndefined\n                })[key] === value;\n            }) ? [\n                ...acc,\n                cvClass,\n                cvClassName\n            ] : acc;\n        }, []);\n        return cx(base, getVariantClassNames, getCompoundVariantClassNames, props === null || props === void 0 ? void 0 : props.class, props === null || props === void 0 ? void 0 : props.className);\n    };\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/class-variance-authority/dist/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs":
/*!*****************************************!*\
  !*** ./node_modules/clsx/dist/clsx.mjs ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clsx: () => (/* binding */ clsx),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nfunction r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f)}else for(f in e)e[f]&&(n&&(n+=\" \"),n+=f);return n}function clsx(){for(var e,t,f=0,n=\"\",o=arguments.length;f<o;f++)(e=arguments[f])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (clsx);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9jbHN4L2Rpc3QvY2xzeC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSxjQUFjLGFBQWEsK0NBQStDLGdEQUFnRCxlQUFlLFFBQVEsSUFBSSwwQ0FBMEMseUNBQXlDLFNBQWdCLGdCQUFnQix3Q0FBd0MsSUFBSSxtREFBbUQsU0FBUyxpRUFBZSxJQUFJIiwic291cmNlcyI6WyJEOlxcbXlhaXR0c1xcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcY2xzeFxcZGlzdFxcY2xzeC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gcihlKXt2YXIgdCxmLG49XCJcIjtpZihcInN0cmluZ1wiPT10eXBlb2YgZXx8XCJudW1iZXJcIj09dHlwZW9mIGUpbis9ZTtlbHNlIGlmKFwib2JqZWN0XCI9PXR5cGVvZiBlKWlmKEFycmF5LmlzQXJyYXkoZSkpe3ZhciBvPWUubGVuZ3RoO2Zvcih0PTA7dDxvO3QrKyllW3RdJiYoZj1yKGVbdF0pKSYmKG4mJihuKz1cIiBcIiksbis9Zil9ZWxzZSBmb3IoZiBpbiBlKWVbZl0mJihuJiYobis9XCIgXCIpLG4rPWYpO3JldHVybiBufWV4cG9ydCBmdW5jdGlvbiBjbHN4KCl7Zm9yKHZhciBlLHQsZj0wLG49XCJcIixvPWFyZ3VtZW50cy5sZW5ndGg7ZjxvO2YrKykoZT1hcmd1bWVudHNbZl0pJiYodD1yKGUpKSYmKG4mJihuKz1cIiBcIiksbis9dCk7cmV0dXJuIG59ZXhwb3J0IGRlZmF1bHQgY2xzeDsiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Capp%5C%5Ctest-auth%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Capp%5C%5Ctest-auth%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/test-auth/page.tsx */ \"(app-pages-browser)/./app/test-auth/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q215YWl0dHMlNUMlNUNmcm9udGVuZCU1QyU1Q2FwcCU1QyU1Q3Rlc3QtYXV0aCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEtBQW9GIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxteWFpdHRzXFxcXGZyb250ZW5kXFxcXGFwcFxcXFx0ZXN0LWF1dGhcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Capp%5C%5Ctest-auth%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PORTAL_TYPE:\n          return \"Portal\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          },\n      specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        Error(\"react-stack-top-frame\"),\n        createTask(getTaskName(type))\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkQ6XFxteWFpdHRzXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxuZXh0XFxkaXN0XFxjb21waWxlZFxccmVhY3RcXGpzeC1kZXYtcnVudGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24uanMnKTtcbn0gZWxzZSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/tailwind-merge/dist/bundle-mjs.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTailwindMerge: () => (/* binding */ createTailwindMerge),\n/* harmony export */   extendTailwindMerge: () => (/* binding */ extendTailwindMerge),\n/* harmony export */   fromTheme: () => (/* binding */ fromTheme),\n/* harmony export */   getDefaultConfig: () => (/* binding */ getDefaultConfig),\n/* harmony export */   mergeConfigs: () => (/* binding */ mergeConfigs),\n/* harmony export */   twJoin: () => (/* binding */ twJoin),\n/* harmony export */   twMerge: () => (/* binding */ twMerge),\n/* harmony export */   validators: () => (/* binding */ validators)\n/* harmony export */ });\nconst CLASS_PART_SEPARATOR = '-';\nconst createClassGroupUtils = config => {\n  const classMap = createClassMap(config);\n  const {\n    conflictingClassGroups,\n    conflictingClassGroupModifiers\n  } = config;\n  const getClassGroupId = className => {\n    const classParts = className.split(CLASS_PART_SEPARATOR);\n    // Classes like `-inset-1` produce an empty string as first classPart. We assume that classes for negative values are used correctly and remove it from classParts.\n    if (classParts[0] === '' && classParts.length !== 1) {\n      classParts.shift();\n    }\n    return getGroupRecursive(classParts, classMap) || getGroupIdForArbitraryProperty(className);\n  };\n  const getConflictingClassGroupIds = (classGroupId, hasPostfixModifier) => {\n    const conflicts = conflictingClassGroups[classGroupId] || [];\n    if (hasPostfixModifier && conflictingClassGroupModifiers[classGroupId]) {\n      return [...conflicts, ...conflictingClassGroupModifiers[classGroupId]];\n    }\n    return conflicts;\n  };\n  return {\n    getClassGroupId,\n    getConflictingClassGroupIds\n  };\n};\nconst getGroupRecursive = (classParts, classPartObject) => {\n  if (classParts.length === 0) {\n    return classPartObject.classGroupId;\n  }\n  const currentClassPart = classParts[0];\n  const nextClassPartObject = classPartObject.nextPart.get(currentClassPart);\n  const classGroupFromNextClassPart = nextClassPartObject ? getGroupRecursive(classParts.slice(1), nextClassPartObject) : undefined;\n  if (classGroupFromNextClassPart) {\n    return classGroupFromNextClassPart;\n  }\n  if (classPartObject.validators.length === 0) {\n    return undefined;\n  }\n  const classRest = classParts.join(CLASS_PART_SEPARATOR);\n  return classPartObject.validators.find(({\n    validator\n  }) => validator(classRest))?.classGroupId;\n};\nconst arbitraryPropertyRegex = /^\\[(.+)\\]$/;\nconst getGroupIdForArbitraryProperty = className => {\n  if (arbitraryPropertyRegex.test(className)) {\n    const arbitraryPropertyClassName = arbitraryPropertyRegex.exec(className)[1];\n    const property = arbitraryPropertyClassName?.substring(0, arbitraryPropertyClassName.indexOf(':'));\n    if (property) {\n      // I use two dots here because one dot is used as prefix for class groups in plugins\n      return 'arbitrary..' + property;\n    }\n  }\n};\n/**\n * Exported for testing only\n */\nconst createClassMap = config => {\n  const {\n    theme,\n    prefix\n  } = config;\n  const classMap = {\n    nextPart: new Map(),\n    validators: []\n  };\n  const prefixedClassGroupEntries = getPrefixedClassGroupEntries(Object.entries(config.classGroups), prefix);\n  prefixedClassGroupEntries.forEach(([classGroupId, classGroup]) => {\n    processClassesRecursively(classGroup, classMap, classGroupId, theme);\n  });\n  return classMap;\n};\nconst processClassesRecursively = (classGroup, classPartObject, classGroupId, theme) => {\n  classGroup.forEach(classDefinition => {\n    if (typeof classDefinition === 'string') {\n      const classPartObjectToEdit = classDefinition === '' ? classPartObject : getPart(classPartObject, classDefinition);\n      classPartObjectToEdit.classGroupId = classGroupId;\n      return;\n    }\n    if (typeof classDefinition === 'function') {\n      if (isThemeGetter(classDefinition)) {\n        processClassesRecursively(classDefinition(theme), classPartObject, classGroupId, theme);\n        return;\n      }\n      classPartObject.validators.push({\n        validator: classDefinition,\n        classGroupId\n      });\n      return;\n    }\n    Object.entries(classDefinition).forEach(([key, classGroup]) => {\n      processClassesRecursively(classGroup, getPart(classPartObject, key), classGroupId, theme);\n    });\n  });\n};\nconst getPart = (classPartObject, path) => {\n  let currentClassPartObject = classPartObject;\n  path.split(CLASS_PART_SEPARATOR).forEach(pathPart => {\n    if (!currentClassPartObject.nextPart.has(pathPart)) {\n      currentClassPartObject.nextPart.set(pathPart, {\n        nextPart: new Map(),\n        validators: []\n      });\n    }\n    currentClassPartObject = currentClassPartObject.nextPart.get(pathPart);\n  });\n  return currentClassPartObject;\n};\nconst isThemeGetter = func => func.isThemeGetter;\nconst getPrefixedClassGroupEntries = (classGroupEntries, prefix) => {\n  if (!prefix) {\n    return classGroupEntries;\n  }\n  return classGroupEntries.map(([classGroupId, classGroup]) => {\n    const prefixedClassGroup = classGroup.map(classDefinition => {\n      if (typeof classDefinition === 'string') {\n        return prefix + classDefinition;\n      }\n      if (typeof classDefinition === 'object') {\n        return Object.fromEntries(Object.entries(classDefinition).map(([key, value]) => [prefix + key, value]));\n      }\n      return classDefinition;\n    });\n    return [classGroupId, prefixedClassGroup];\n  });\n};\n\n// LRU cache inspired from hashlru (https://github.com/dominictarr/hashlru/blob/v1.0.4/index.js) but object replaced with Map to improve performance\nconst createLruCache = maxCacheSize => {\n  if (maxCacheSize < 1) {\n    return {\n      get: () => undefined,\n      set: () => {}\n    };\n  }\n  let cacheSize = 0;\n  let cache = new Map();\n  let previousCache = new Map();\n  const update = (key, value) => {\n    cache.set(key, value);\n    cacheSize++;\n    if (cacheSize > maxCacheSize) {\n      cacheSize = 0;\n      previousCache = cache;\n      cache = new Map();\n    }\n  };\n  return {\n    get(key) {\n      let value = cache.get(key);\n      if (value !== undefined) {\n        return value;\n      }\n      if ((value = previousCache.get(key)) !== undefined) {\n        update(key, value);\n        return value;\n      }\n    },\n    set(key, value) {\n      if (cache.has(key)) {\n        cache.set(key, value);\n      } else {\n        update(key, value);\n      }\n    }\n  };\n};\nconst IMPORTANT_MODIFIER = '!';\nconst createParseClassName = config => {\n  const {\n    separator,\n    experimentalParseClassName\n  } = config;\n  const isSeparatorSingleCharacter = separator.length === 1;\n  const firstSeparatorCharacter = separator[0];\n  const separatorLength = separator.length;\n  // parseClassName inspired by https://github.com/tailwindlabs/tailwindcss/blob/v3.2.2/src/util/splitAtTopLevelOnly.js\n  const parseClassName = className => {\n    const modifiers = [];\n    let bracketDepth = 0;\n    let modifierStart = 0;\n    let postfixModifierPosition;\n    for (let index = 0; index < className.length; index++) {\n      let currentCharacter = className[index];\n      if (bracketDepth === 0) {\n        if (currentCharacter === firstSeparatorCharacter && (isSeparatorSingleCharacter || className.slice(index, index + separatorLength) === separator)) {\n          modifiers.push(className.slice(modifierStart, index));\n          modifierStart = index + separatorLength;\n          continue;\n        }\n        if (currentCharacter === '/') {\n          postfixModifierPosition = index;\n          continue;\n        }\n      }\n      if (currentCharacter === '[') {\n        bracketDepth++;\n      } else if (currentCharacter === ']') {\n        bracketDepth--;\n      }\n    }\n    const baseClassNameWithImportantModifier = modifiers.length === 0 ? className : className.substring(modifierStart);\n    const hasImportantModifier = baseClassNameWithImportantModifier.startsWith(IMPORTANT_MODIFIER);\n    const baseClassName = hasImportantModifier ? baseClassNameWithImportantModifier.substring(1) : baseClassNameWithImportantModifier;\n    const maybePostfixModifierPosition = postfixModifierPosition && postfixModifierPosition > modifierStart ? postfixModifierPosition - modifierStart : undefined;\n    return {\n      modifiers,\n      hasImportantModifier,\n      baseClassName,\n      maybePostfixModifierPosition\n    };\n  };\n  if (experimentalParseClassName) {\n    return className => experimentalParseClassName({\n      className,\n      parseClassName\n    });\n  }\n  return parseClassName;\n};\n/**\n * Sorts modifiers according to following schema:\n * - Predefined modifiers are sorted alphabetically\n * - When an arbitrary variant appears, it must be preserved which modifiers are before and after it\n */\nconst sortModifiers = modifiers => {\n  if (modifiers.length <= 1) {\n    return modifiers;\n  }\n  const sortedModifiers = [];\n  let unsortedModifiers = [];\n  modifiers.forEach(modifier => {\n    const isArbitraryVariant = modifier[0] === '[';\n    if (isArbitraryVariant) {\n      sortedModifiers.push(...unsortedModifiers.sort(), modifier);\n      unsortedModifiers = [];\n    } else {\n      unsortedModifiers.push(modifier);\n    }\n  });\n  sortedModifiers.push(...unsortedModifiers.sort());\n  return sortedModifiers;\n};\nconst createConfigUtils = config => ({\n  cache: createLruCache(config.cacheSize),\n  parseClassName: createParseClassName(config),\n  ...createClassGroupUtils(config)\n});\nconst SPLIT_CLASSES_REGEX = /\\s+/;\nconst mergeClassList = (classList, configUtils) => {\n  const {\n    parseClassName,\n    getClassGroupId,\n    getConflictingClassGroupIds\n  } = configUtils;\n  /**\n   * Set of classGroupIds in following format:\n   * `{importantModifier}{variantModifiers}{classGroupId}`\n   * @example 'float'\n   * @example 'hover:focus:bg-color'\n   * @example 'md:!pr'\n   */\n  const classGroupsInConflict = [];\n  const classNames = classList.trim().split(SPLIT_CLASSES_REGEX);\n  let result = '';\n  for (let index = classNames.length - 1; index >= 0; index -= 1) {\n    const originalClassName = classNames[index];\n    const {\n      modifiers,\n      hasImportantModifier,\n      baseClassName,\n      maybePostfixModifierPosition\n    } = parseClassName(originalClassName);\n    let hasPostfixModifier = Boolean(maybePostfixModifierPosition);\n    let classGroupId = getClassGroupId(hasPostfixModifier ? baseClassName.substring(0, maybePostfixModifierPosition) : baseClassName);\n    if (!classGroupId) {\n      if (!hasPostfixModifier) {\n        // Not a Tailwind class\n        result = originalClassName + (result.length > 0 ? ' ' + result : result);\n        continue;\n      }\n      classGroupId = getClassGroupId(baseClassName);\n      if (!classGroupId) {\n        // Not a Tailwind class\n        result = originalClassName + (result.length > 0 ? ' ' + result : result);\n        continue;\n      }\n      hasPostfixModifier = false;\n    }\n    const variantModifier = sortModifiers(modifiers).join(':');\n    const modifierId = hasImportantModifier ? variantModifier + IMPORTANT_MODIFIER : variantModifier;\n    const classId = modifierId + classGroupId;\n    if (classGroupsInConflict.includes(classId)) {\n      // Tailwind class omitted due to conflict\n      continue;\n    }\n    classGroupsInConflict.push(classId);\n    const conflictGroups = getConflictingClassGroupIds(classGroupId, hasPostfixModifier);\n    for (let i = 0; i < conflictGroups.length; ++i) {\n      const group = conflictGroups[i];\n      classGroupsInConflict.push(modifierId + group);\n    }\n    // Tailwind class not in conflict\n    result = originalClassName + (result.length > 0 ? ' ' + result : result);\n  }\n  return result;\n};\n\n/**\n * The code in this file is copied from https://github.com/lukeed/clsx and modified to suit the needs of tailwind-merge better.\n *\n * Specifically:\n * - Runtime code from https://github.com/lukeed/clsx/blob/v1.2.1/src/index.js\n * - TypeScript types from https://github.com/lukeed/clsx/blob/v1.2.1/clsx.d.ts\n *\n * Original code has MIT license: Copyright (c) Luke Edwards <<EMAIL>> (lukeed.com)\n */\nfunction twJoin() {\n  let index = 0;\n  let argument;\n  let resolvedValue;\n  let string = '';\n  while (index < arguments.length) {\n    if (argument = arguments[index++]) {\n      if (resolvedValue = toValue(argument)) {\n        string && (string += ' ');\n        string += resolvedValue;\n      }\n    }\n  }\n  return string;\n}\nconst toValue = mix => {\n  if (typeof mix === 'string') {\n    return mix;\n  }\n  let resolvedValue;\n  let string = '';\n  for (let k = 0; k < mix.length; k++) {\n    if (mix[k]) {\n      if (resolvedValue = toValue(mix[k])) {\n        string && (string += ' ');\n        string += resolvedValue;\n      }\n    }\n  }\n  return string;\n};\nfunction createTailwindMerge(createConfigFirst, ...createConfigRest) {\n  let configUtils;\n  let cacheGet;\n  let cacheSet;\n  let functionToCall = initTailwindMerge;\n  function initTailwindMerge(classList) {\n    const config = createConfigRest.reduce((previousConfig, createConfigCurrent) => createConfigCurrent(previousConfig), createConfigFirst());\n    configUtils = createConfigUtils(config);\n    cacheGet = configUtils.cache.get;\n    cacheSet = configUtils.cache.set;\n    functionToCall = tailwindMerge;\n    return tailwindMerge(classList);\n  }\n  function tailwindMerge(classList) {\n    const cachedResult = cacheGet(classList);\n    if (cachedResult) {\n      return cachedResult;\n    }\n    const result = mergeClassList(classList, configUtils);\n    cacheSet(classList, result);\n    return result;\n  }\n  return function callTailwindMerge() {\n    return functionToCall(twJoin.apply(null, arguments));\n  };\n}\nconst fromTheme = key => {\n  const themeGetter = theme => theme[key] || [];\n  themeGetter.isThemeGetter = true;\n  return themeGetter;\n};\nconst arbitraryValueRegex = /^\\[(?:([a-z-]+):)?(.+)\\]$/i;\nconst fractionRegex = /^\\d+\\/\\d+$/;\nconst stringLengths = /*#__PURE__*/new Set(['px', 'full', 'screen']);\nconst tshirtUnitRegex = /^(\\d+(\\.\\d+)?)?(xs|sm|md|lg|xl)$/;\nconst lengthUnitRegex = /\\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\\b(calc|min|max|clamp)\\(.+\\)|^0$/;\nconst colorFunctionRegex = /^(rgba?|hsla?|hwb|(ok)?(lab|lch))\\(.+\\)$/;\n// Shadow always begins with x and y offset separated by underscore optionally prepended by inset\nconst shadowRegex = /^(inset_)?-?((\\d+)?\\.?(\\d+)[a-z]+|0)_-?((\\d+)?\\.?(\\d+)[a-z]+|0)/;\nconst imageRegex = /^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\\(.+\\)$/;\nconst isLength = value => isNumber(value) || stringLengths.has(value) || fractionRegex.test(value);\nconst isArbitraryLength = value => getIsArbitraryValue(value, 'length', isLengthOnly);\nconst isNumber = value => Boolean(value) && !Number.isNaN(Number(value));\nconst isArbitraryNumber = value => getIsArbitraryValue(value, 'number', isNumber);\nconst isInteger = value => Boolean(value) && Number.isInteger(Number(value));\nconst isPercent = value => value.endsWith('%') && isNumber(value.slice(0, -1));\nconst isArbitraryValue = value => arbitraryValueRegex.test(value);\nconst isTshirtSize = value => tshirtUnitRegex.test(value);\nconst sizeLabels = /*#__PURE__*/new Set(['length', 'size', 'percentage']);\nconst isArbitrarySize = value => getIsArbitraryValue(value, sizeLabels, isNever);\nconst isArbitraryPosition = value => getIsArbitraryValue(value, 'position', isNever);\nconst imageLabels = /*#__PURE__*/new Set(['image', 'url']);\nconst isArbitraryImage = value => getIsArbitraryValue(value, imageLabels, isImage);\nconst isArbitraryShadow = value => getIsArbitraryValue(value, '', isShadow);\nconst isAny = () => true;\nconst getIsArbitraryValue = (value, label, testValue) => {\n  const result = arbitraryValueRegex.exec(value);\n  if (result) {\n    if (result[1]) {\n      return typeof label === 'string' ? result[1] === label : label.has(result[1]);\n    }\n    return testValue(result[2]);\n  }\n  return false;\n};\nconst isLengthOnly = value =>\n// `colorFunctionRegex` check is necessary because color functions can have percentages in them which which would be incorrectly classified as lengths.\n// For example, `hsl(0 0% 0%)` would be classified as a length without this check.\n// I could also use lookbehind assertion in `lengthUnitRegex` but that isn't supported widely enough.\nlengthUnitRegex.test(value) && !colorFunctionRegex.test(value);\nconst isNever = () => false;\nconst isShadow = value => shadowRegex.test(value);\nconst isImage = value => imageRegex.test(value);\nconst validators = /*#__PURE__*/Object.defineProperty({\n  __proto__: null,\n  isAny,\n  isArbitraryImage,\n  isArbitraryLength,\n  isArbitraryNumber,\n  isArbitraryPosition,\n  isArbitraryShadow,\n  isArbitrarySize,\n  isArbitraryValue,\n  isInteger,\n  isLength,\n  isNumber,\n  isPercent,\n  isTshirtSize\n}, Symbol.toStringTag, {\n  value: 'Module'\n});\nconst getDefaultConfig = () => {\n  const colors = fromTheme('colors');\n  const spacing = fromTheme('spacing');\n  const blur = fromTheme('blur');\n  const brightness = fromTheme('brightness');\n  const borderColor = fromTheme('borderColor');\n  const borderRadius = fromTheme('borderRadius');\n  const borderSpacing = fromTheme('borderSpacing');\n  const borderWidth = fromTheme('borderWidth');\n  const contrast = fromTheme('contrast');\n  const grayscale = fromTheme('grayscale');\n  const hueRotate = fromTheme('hueRotate');\n  const invert = fromTheme('invert');\n  const gap = fromTheme('gap');\n  const gradientColorStops = fromTheme('gradientColorStops');\n  const gradientColorStopPositions = fromTheme('gradientColorStopPositions');\n  const inset = fromTheme('inset');\n  const margin = fromTheme('margin');\n  const opacity = fromTheme('opacity');\n  const padding = fromTheme('padding');\n  const saturate = fromTheme('saturate');\n  const scale = fromTheme('scale');\n  const sepia = fromTheme('sepia');\n  const skew = fromTheme('skew');\n  const space = fromTheme('space');\n  const translate = fromTheme('translate');\n  const getOverscroll = () => ['auto', 'contain', 'none'];\n  const getOverflow = () => ['auto', 'hidden', 'clip', 'visible', 'scroll'];\n  const getSpacingWithAutoAndArbitrary = () => ['auto', isArbitraryValue, spacing];\n  const getSpacingWithArbitrary = () => [isArbitraryValue, spacing];\n  const getLengthWithEmptyAndArbitrary = () => ['', isLength, isArbitraryLength];\n  const getNumberWithAutoAndArbitrary = () => ['auto', isNumber, isArbitraryValue];\n  const getPositions = () => ['bottom', 'center', 'left', 'left-bottom', 'left-top', 'right', 'right-bottom', 'right-top', 'top'];\n  const getLineStyles = () => ['solid', 'dashed', 'dotted', 'double', 'none'];\n  const getBlendModes = () => ['normal', 'multiply', 'screen', 'overlay', 'darken', 'lighten', 'color-dodge', 'color-burn', 'hard-light', 'soft-light', 'difference', 'exclusion', 'hue', 'saturation', 'color', 'luminosity'];\n  const getAlign = () => ['start', 'end', 'center', 'between', 'around', 'evenly', 'stretch'];\n  const getZeroAndEmpty = () => ['', '0', isArbitraryValue];\n  const getBreaks = () => ['auto', 'avoid', 'all', 'avoid-page', 'page', 'left', 'right', 'column'];\n  const getNumberAndArbitrary = () => [isNumber, isArbitraryValue];\n  return {\n    cacheSize: 500,\n    separator: ':',\n    theme: {\n      colors: [isAny],\n      spacing: [isLength, isArbitraryLength],\n      blur: ['none', '', isTshirtSize, isArbitraryValue],\n      brightness: getNumberAndArbitrary(),\n      borderColor: [colors],\n      borderRadius: ['none', '', 'full', isTshirtSize, isArbitraryValue],\n      borderSpacing: getSpacingWithArbitrary(),\n      borderWidth: getLengthWithEmptyAndArbitrary(),\n      contrast: getNumberAndArbitrary(),\n      grayscale: getZeroAndEmpty(),\n      hueRotate: getNumberAndArbitrary(),\n      invert: getZeroAndEmpty(),\n      gap: getSpacingWithArbitrary(),\n      gradientColorStops: [colors],\n      gradientColorStopPositions: [isPercent, isArbitraryLength],\n      inset: getSpacingWithAutoAndArbitrary(),\n      margin: getSpacingWithAutoAndArbitrary(),\n      opacity: getNumberAndArbitrary(),\n      padding: getSpacingWithArbitrary(),\n      saturate: getNumberAndArbitrary(),\n      scale: getNumberAndArbitrary(),\n      sepia: getZeroAndEmpty(),\n      skew: getNumberAndArbitrary(),\n      space: getSpacingWithArbitrary(),\n      translate: getSpacingWithArbitrary()\n    },\n    classGroups: {\n      // Layout\n      /**\n       * Aspect Ratio\n       * @see https://tailwindcss.com/docs/aspect-ratio\n       */\n      aspect: [{\n        aspect: ['auto', 'square', 'video', isArbitraryValue]\n      }],\n      /**\n       * Container\n       * @see https://tailwindcss.com/docs/container\n       */\n      container: ['container'],\n      /**\n       * Columns\n       * @see https://tailwindcss.com/docs/columns\n       */\n      columns: [{\n        columns: [isTshirtSize]\n      }],\n      /**\n       * Break After\n       * @see https://tailwindcss.com/docs/break-after\n       */\n      'break-after': [{\n        'break-after': getBreaks()\n      }],\n      /**\n       * Break Before\n       * @see https://tailwindcss.com/docs/break-before\n       */\n      'break-before': [{\n        'break-before': getBreaks()\n      }],\n      /**\n       * Break Inside\n       * @see https://tailwindcss.com/docs/break-inside\n       */\n      'break-inside': [{\n        'break-inside': ['auto', 'avoid', 'avoid-page', 'avoid-column']\n      }],\n      /**\n       * Box Decoration Break\n       * @see https://tailwindcss.com/docs/box-decoration-break\n       */\n      'box-decoration': [{\n        'box-decoration': ['slice', 'clone']\n      }],\n      /**\n       * Box Sizing\n       * @see https://tailwindcss.com/docs/box-sizing\n       */\n      box: [{\n        box: ['border', 'content']\n      }],\n      /**\n       * Display\n       * @see https://tailwindcss.com/docs/display\n       */\n      display: ['block', 'inline-block', 'inline', 'flex', 'inline-flex', 'table', 'inline-table', 'table-caption', 'table-cell', 'table-column', 'table-column-group', 'table-footer-group', 'table-header-group', 'table-row-group', 'table-row', 'flow-root', 'grid', 'inline-grid', 'contents', 'list-item', 'hidden'],\n      /**\n       * Floats\n       * @see https://tailwindcss.com/docs/float\n       */\n      float: [{\n        float: ['right', 'left', 'none', 'start', 'end']\n      }],\n      /**\n       * Clear\n       * @see https://tailwindcss.com/docs/clear\n       */\n      clear: [{\n        clear: ['left', 'right', 'both', 'none', 'start', 'end']\n      }],\n      /**\n       * Isolation\n       * @see https://tailwindcss.com/docs/isolation\n       */\n      isolation: ['isolate', 'isolation-auto'],\n      /**\n       * Object Fit\n       * @see https://tailwindcss.com/docs/object-fit\n       */\n      'object-fit': [{\n        object: ['contain', 'cover', 'fill', 'none', 'scale-down']\n      }],\n      /**\n       * Object Position\n       * @see https://tailwindcss.com/docs/object-position\n       */\n      'object-position': [{\n        object: [...getPositions(), isArbitraryValue]\n      }],\n      /**\n       * Overflow\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      overflow: [{\n        overflow: getOverflow()\n      }],\n      /**\n       * Overflow X\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      'overflow-x': [{\n        'overflow-x': getOverflow()\n      }],\n      /**\n       * Overflow Y\n       * @see https://tailwindcss.com/docs/overflow\n       */\n      'overflow-y': [{\n        'overflow-y': getOverflow()\n      }],\n      /**\n       * Overscroll Behavior\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      overscroll: [{\n        overscroll: getOverscroll()\n      }],\n      /**\n       * Overscroll Behavior X\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      'overscroll-x': [{\n        'overscroll-x': getOverscroll()\n      }],\n      /**\n       * Overscroll Behavior Y\n       * @see https://tailwindcss.com/docs/overscroll-behavior\n       */\n      'overscroll-y': [{\n        'overscroll-y': getOverscroll()\n      }],\n      /**\n       * Position\n       * @see https://tailwindcss.com/docs/position\n       */\n      position: ['static', 'fixed', 'absolute', 'relative', 'sticky'],\n      /**\n       * Top / Right / Bottom / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      inset: [{\n        inset: [inset]\n      }],\n      /**\n       * Right / Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      'inset-x': [{\n        'inset-x': [inset]\n      }],\n      /**\n       * Top / Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      'inset-y': [{\n        'inset-y': [inset]\n      }],\n      /**\n       * Start\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      start: [{\n        start: [inset]\n      }],\n      /**\n       * End\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      end: [{\n        end: [inset]\n      }],\n      /**\n       * Top\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      top: [{\n        top: [inset]\n      }],\n      /**\n       * Right\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      right: [{\n        right: [inset]\n      }],\n      /**\n       * Bottom\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      bottom: [{\n        bottom: [inset]\n      }],\n      /**\n       * Left\n       * @see https://tailwindcss.com/docs/top-right-bottom-left\n       */\n      left: [{\n        left: [inset]\n      }],\n      /**\n       * Visibility\n       * @see https://tailwindcss.com/docs/visibility\n       */\n      visibility: ['visible', 'invisible', 'collapse'],\n      /**\n       * Z-Index\n       * @see https://tailwindcss.com/docs/z-index\n       */\n      z: [{\n        z: ['auto', isInteger, isArbitraryValue]\n      }],\n      // Flexbox and Grid\n      /**\n       * Flex Basis\n       * @see https://tailwindcss.com/docs/flex-basis\n       */\n      basis: [{\n        basis: getSpacingWithAutoAndArbitrary()\n      }],\n      /**\n       * Flex Direction\n       * @see https://tailwindcss.com/docs/flex-direction\n       */\n      'flex-direction': [{\n        flex: ['row', 'row-reverse', 'col', 'col-reverse']\n      }],\n      /**\n       * Flex Wrap\n       * @see https://tailwindcss.com/docs/flex-wrap\n       */\n      'flex-wrap': [{\n        flex: ['wrap', 'wrap-reverse', 'nowrap']\n      }],\n      /**\n       * Flex\n       * @see https://tailwindcss.com/docs/flex\n       */\n      flex: [{\n        flex: ['1', 'auto', 'initial', 'none', isArbitraryValue]\n      }],\n      /**\n       * Flex Grow\n       * @see https://tailwindcss.com/docs/flex-grow\n       */\n      grow: [{\n        grow: getZeroAndEmpty()\n      }],\n      /**\n       * Flex Shrink\n       * @see https://tailwindcss.com/docs/flex-shrink\n       */\n      shrink: [{\n        shrink: getZeroAndEmpty()\n      }],\n      /**\n       * Order\n       * @see https://tailwindcss.com/docs/order\n       */\n      order: [{\n        order: ['first', 'last', 'none', isInteger, isArbitraryValue]\n      }],\n      /**\n       * Grid Template Columns\n       * @see https://tailwindcss.com/docs/grid-template-columns\n       */\n      'grid-cols': [{\n        'grid-cols': [isAny]\n      }],\n      /**\n       * Grid Column Start / End\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-start-end': [{\n        col: ['auto', {\n          span: ['full', isInteger, isArbitraryValue]\n        }, isArbitraryValue]\n      }],\n      /**\n       * Grid Column Start\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-start': [{\n        'col-start': getNumberWithAutoAndArbitrary()\n      }],\n      /**\n       * Grid Column End\n       * @see https://tailwindcss.com/docs/grid-column\n       */\n      'col-end': [{\n        'col-end': getNumberWithAutoAndArbitrary()\n      }],\n      /**\n       * Grid Template Rows\n       * @see https://tailwindcss.com/docs/grid-template-rows\n       */\n      'grid-rows': [{\n        'grid-rows': [isAny]\n      }],\n      /**\n       * Grid Row Start / End\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-start-end': [{\n        row: ['auto', {\n          span: [isInteger, isArbitraryValue]\n        }, isArbitraryValue]\n      }],\n      /**\n       * Grid Row Start\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-start': [{\n        'row-start': getNumberWithAutoAndArbitrary()\n      }],\n      /**\n       * Grid Row End\n       * @see https://tailwindcss.com/docs/grid-row\n       */\n      'row-end': [{\n        'row-end': getNumberWithAutoAndArbitrary()\n      }],\n      /**\n       * Grid Auto Flow\n       * @see https://tailwindcss.com/docs/grid-auto-flow\n       */\n      'grid-flow': [{\n        'grid-flow': ['row', 'col', 'dense', 'row-dense', 'col-dense']\n      }],\n      /**\n       * Grid Auto Columns\n       * @see https://tailwindcss.com/docs/grid-auto-columns\n       */\n      'auto-cols': [{\n        'auto-cols': ['auto', 'min', 'max', 'fr', isArbitraryValue]\n      }],\n      /**\n       * Grid Auto Rows\n       * @see https://tailwindcss.com/docs/grid-auto-rows\n       */\n      'auto-rows': [{\n        'auto-rows': ['auto', 'min', 'max', 'fr', isArbitraryValue]\n      }],\n      /**\n       * Gap\n       * @see https://tailwindcss.com/docs/gap\n       */\n      gap: [{\n        gap: [gap]\n      }],\n      /**\n       * Gap X\n       * @see https://tailwindcss.com/docs/gap\n       */\n      'gap-x': [{\n        'gap-x': [gap]\n      }],\n      /**\n       * Gap Y\n       * @see https://tailwindcss.com/docs/gap\n       */\n      'gap-y': [{\n        'gap-y': [gap]\n      }],\n      /**\n       * Justify Content\n       * @see https://tailwindcss.com/docs/justify-content\n       */\n      'justify-content': [{\n        justify: ['normal', ...getAlign()]\n      }],\n      /**\n       * Justify Items\n       * @see https://tailwindcss.com/docs/justify-items\n       */\n      'justify-items': [{\n        'justify-items': ['start', 'end', 'center', 'stretch']\n      }],\n      /**\n       * Justify Self\n       * @see https://tailwindcss.com/docs/justify-self\n       */\n      'justify-self': [{\n        'justify-self': ['auto', 'start', 'end', 'center', 'stretch']\n      }],\n      /**\n       * Align Content\n       * @see https://tailwindcss.com/docs/align-content\n       */\n      'align-content': [{\n        content: ['normal', ...getAlign(), 'baseline']\n      }],\n      /**\n       * Align Items\n       * @see https://tailwindcss.com/docs/align-items\n       */\n      'align-items': [{\n        items: ['start', 'end', 'center', 'baseline', 'stretch']\n      }],\n      /**\n       * Align Self\n       * @see https://tailwindcss.com/docs/align-self\n       */\n      'align-self': [{\n        self: ['auto', 'start', 'end', 'center', 'stretch', 'baseline']\n      }],\n      /**\n       * Place Content\n       * @see https://tailwindcss.com/docs/place-content\n       */\n      'place-content': [{\n        'place-content': [...getAlign(), 'baseline']\n      }],\n      /**\n       * Place Items\n       * @see https://tailwindcss.com/docs/place-items\n       */\n      'place-items': [{\n        'place-items': ['start', 'end', 'center', 'baseline', 'stretch']\n      }],\n      /**\n       * Place Self\n       * @see https://tailwindcss.com/docs/place-self\n       */\n      'place-self': [{\n        'place-self': ['auto', 'start', 'end', 'center', 'stretch']\n      }],\n      // Spacing\n      /**\n       * Padding\n       * @see https://tailwindcss.com/docs/padding\n       */\n      p: [{\n        p: [padding]\n      }],\n      /**\n       * Padding X\n       * @see https://tailwindcss.com/docs/padding\n       */\n      px: [{\n        px: [padding]\n      }],\n      /**\n       * Padding Y\n       * @see https://tailwindcss.com/docs/padding\n       */\n      py: [{\n        py: [padding]\n      }],\n      /**\n       * Padding Start\n       * @see https://tailwindcss.com/docs/padding\n       */\n      ps: [{\n        ps: [padding]\n      }],\n      /**\n       * Padding End\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pe: [{\n        pe: [padding]\n      }],\n      /**\n       * Padding Top\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pt: [{\n        pt: [padding]\n      }],\n      /**\n       * Padding Right\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pr: [{\n        pr: [padding]\n      }],\n      /**\n       * Padding Bottom\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pb: [{\n        pb: [padding]\n      }],\n      /**\n       * Padding Left\n       * @see https://tailwindcss.com/docs/padding\n       */\n      pl: [{\n        pl: [padding]\n      }],\n      /**\n       * Margin\n       * @see https://tailwindcss.com/docs/margin\n       */\n      m: [{\n        m: [margin]\n      }],\n      /**\n       * Margin X\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mx: [{\n        mx: [margin]\n      }],\n      /**\n       * Margin Y\n       * @see https://tailwindcss.com/docs/margin\n       */\n      my: [{\n        my: [margin]\n      }],\n      /**\n       * Margin Start\n       * @see https://tailwindcss.com/docs/margin\n       */\n      ms: [{\n        ms: [margin]\n      }],\n      /**\n       * Margin End\n       * @see https://tailwindcss.com/docs/margin\n       */\n      me: [{\n        me: [margin]\n      }],\n      /**\n       * Margin Top\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mt: [{\n        mt: [margin]\n      }],\n      /**\n       * Margin Right\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mr: [{\n        mr: [margin]\n      }],\n      /**\n       * Margin Bottom\n       * @see https://tailwindcss.com/docs/margin\n       */\n      mb: [{\n        mb: [margin]\n      }],\n      /**\n       * Margin Left\n       * @see https://tailwindcss.com/docs/margin\n       */\n      ml: [{\n        ml: [margin]\n      }],\n      /**\n       * Space Between X\n       * @see https://tailwindcss.com/docs/space\n       */\n      'space-x': [{\n        'space-x': [space]\n      }],\n      /**\n       * Space Between X Reverse\n       * @see https://tailwindcss.com/docs/space\n       */\n      'space-x-reverse': ['space-x-reverse'],\n      /**\n       * Space Between Y\n       * @see https://tailwindcss.com/docs/space\n       */\n      'space-y': [{\n        'space-y': [space]\n      }],\n      /**\n       * Space Between Y Reverse\n       * @see https://tailwindcss.com/docs/space\n       */\n      'space-y-reverse': ['space-y-reverse'],\n      // Sizing\n      /**\n       * Width\n       * @see https://tailwindcss.com/docs/width\n       */\n      w: [{\n        w: ['auto', 'min', 'max', 'fit', 'svw', 'lvw', 'dvw', isArbitraryValue, spacing]\n      }],\n      /**\n       * Min-Width\n       * @see https://tailwindcss.com/docs/min-width\n       */\n      'min-w': [{\n        'min-w': [isArbitraryValue, spacing, 'min', 'max', 'fit']\n      }],\n      /**\n       * Max-Width\n       * @see https://tailwindcss.com/docs/max-width\n       */\n      'max-w': [{\n        'max-w': [isArbitraryValue, spacing, 'none', 'full', 'min', 'max', 'fit', 'prose', {\n          screen: [isTshirtSize]\n        }, isTshirtSize]\n      }],\n      /**\n       * Height\n       * @see https://tailwindcss.com/docs/height\n       */\n      h: [{\n        h: [isArbitraryValue, spacing, 'auto', 'min', 'max', 'fit', 'svh', 'lvh', 'dvh']\n      }],\n      /**\n       * Min-Height\n       * @see https://tailwindcss.com/docs/min-height\n       */\n      'min-h': [{\n        'min-h': [isArbitraryValue, spacing, 'min', 'max', 'fit', 'svh', 'lvh', 'dvh']\n      }],\n      /**\n       * Max-Height\n       * @see https://tailwindcss.com/docs/max-height\n       */\n      'max-h': [{\n        'max-h': [isArbitraryValue, spacing, 'min', 'max', 'fit', 'svh', 'lvh', 'dvh']\n      }],\n      /**\n       * Size\n       * @see https://tailwindcss.com/docs/size\n       */\n      size: [{\n        size: [isArbitraryValue, spacing, 'auto', 'min', 'max', 'fit']\n      }],\n      // Typography\n      /**\n       * Font Size\n       * @see https://tailwindcss.com/docs/font-size\n       */\n      'font-size': [{\n        text: ['base', isTshirtSize, isArbitraryLength]\n      }],\n      /**\n       * Font Smoothing\n       * @see https://tailwindcss.com/docs/font-smoothing\n       */\n      'font-smoothing': ['antialiased', 'subpixel-antialiased'],\n      /**\n       * Font Style\n       * @see https://tailwindcss.com/docs/font-style\n       */\n      'font-style': ['italic', 'not-italic'],\n      /**\n       * Font Weight\n       * @see https://tailwindcss.com/docs/font-weight\n       */\n      'font-weight': [{\n        font: ['thin', 'extralight', 'light', 'normal', 'medium', 'semibold', 'bold', 'extrabold', 'black', isArbitraryNumber]\n      }],\n      /**\n       * Font Family\n       * @see https://tailwindcss.com/docs/font-family\n       */\n      'font-family': [{\n        font: [isAny]\n      }],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-normal': ['normal-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-ordinal': ['ordinal'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-slashed-zero': ['slashed-zero'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-figure': ['lining-nums', 'oldstyle-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-spacing': ['proportional-nums', 'tabular-nums'],\n      /**\n       * Font Variant Numeric\n       * @see https://tailwindcss.com/docs/font-variant-numeric\n       */\n      'fvn-fraction': ['diagonal-fractions', 'stacked-fractions'],\n      /**\n       * Letter Spacing\n       * @see https://tailwindcss.com/docs/letter-spacing\n       */\n      tracking: [{\n        tracking: ['tighter', 'tight', 'normal', 'wide', 'wider', 'widest', isArbitraryValue]\n      }],\n      /**\n       * Line Clamp\n       * @see https://tailwindcss.com/docs/line-clamp\n       */\n      'line-clamp': [{\n        'line-clamp': ['none', isNumber, isArbitraryNumber]\n      }],\n      /**\n       * Line Height\n       * @see https://tailwindcss.com/docs/line-height\n       */\n      leading: [{\n        leading: ['none', 'tight', 'snug', 'normal', 'relaxed', 'loose', isLength, isArbitraryValue]\n      }],\n      /**\n       * List Style Image\n       * @see https://tailwindcss.com/docs/list-style-image\n       */\n      'list-image': [{\n        'list-image': ['none', isArbitraryValue]\n      }],\n      /**\n       * List Style Type\n       * @see https://tailwindcss.com/docs/list-style-type\n       */\n      'list-style-type': [{\n        list: ['none', 'disc', 'decimal', isArbitraryValue]\n      }],\n      /**\n       * List Style Position\n       * @see https://tailwindcss.com/docs/list-style-position\n       */\n      'list-style-position': [{\n        list: ['inside', 'outside']\n      }],\n      /**\n       * Placeholder Color\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://tailwindcss.com/docs/placeholder-color\n       */\n      'placeholder-color': [{\n        placeholder: [colors]\n      }],\n      /**\n       * Placeholder Opacity\n       * @see https://tailwindcss.com/docs/placeholder-opacity\n       */\n      'placeholder-opacity': [{\n        'placeholder-opacity': [opacity]\n      }],\n      /**\n       * Text Alignment\n       * @see https://tailwindcss.com/docs/text-align\n       */\n      'text-alignment': [{\n        text: ['left', 'center', 'right', 'justify', 'start', 'end']\n      }],\n      /**\n       * Text Color\n       * @see https://tailwindcss.com/docs/text-color\n       */\n      'text-color': [{\n        text: [colors]\n      }],\n      /**\n       * Text Opacity\n       * @see https://tailwindcss.com/docs/text-opacity\n       */\n      'text-opacity': [{\n        'text-opacity': [opacity]\n      }],\n      /**\n       * Text Decoration\n       * @see https://tailwindcss.com/docs/text-decoration\n       */\n      'text-decoration': ['underline', 'overline', 'line-through', 'no-underline'],\n      /**\n       * Text Decoration Style\n       * @see https://tailwindcss.com/docs/text-decoration-style\n       */\n      'text-decoration-style': [{\n        decoration: [...getLineStyles(), 'wavy']\n      }],\n      /**\n       * Text Decoration Thickness\n       * @see https://tailwindcss.com/docs/text-decoration-thickness\n       */\n      'text-decoration-thickness': [{\n        decoration: ['auto', 'from-font', isLength, isArbitraryLength]\n      }],\n      /**\n       * Text Underline Offset\n       * @see https://tailwindcss.com/docs/text-underline-offset\n       */\n      'underline-offset': [{\n        'underline-offset': ['auto', isLength, isArbitraryValue]\n      }],\n      /**\n       * Text Decoration Color\n       * @see https://tailwindcss.com/docs/text-decoration-color\n       */\n      'text-decoration-color': [{\n        decoration: [colors]\n      }],\n      /**\n       * Text Transform\n       * @see https://tailwindcss.com/docs/text-transform\n       */\n      'text-transform': ['uppercase', 'lowercase', 'capitalize', 'normal-case'],\n      /**\n       * Text Overflow\n       * @see https://tailwindcss.com/docs/text-overflow\n       */\n      'text-overflow': ['truncate', 'text-ellipsis', 'text-clip'],\n      /**\n       * Text Wrap\n       * @see https://tailwindcss.com/docs/text-wrap\n       */\n      'text-wrap': [{\n        text: ['wrap', 'nowrap', 'balance', 'pretty']\n      }],\n      /**\n       * Text Indent\n       * @see https://tailwindcss.com/docs/text-indent\n       */\n      indent: [{\n        indent: getSpacingWithArbitrary()\n      }],\n      /**\n       * Vertical Alignment\n       * @see https://tailwindcss.com/docs/vertical-align\n       */\n      'vertical-align': [{\n        align: ['baseline', 'top', 'middle', 'bottom', 'text-top', 'text-bottom', 'sub', 'super', isArbitraryValue]\n      }],\n      /**\n       * Whitespace\n       * @see https://tailwindcss.com/docs/whitespace\n       */\n      whitespace: [{\n        whitespace: ['normal', 'nowrap', 'pre', 'pre-line', 'pre-wrap', 'break-spaces']\n      }],\n      /**\n       * Word Break\n       * @see https://tailwindcss.com/docs/word-break\n       */\n      break: [{\n        break: ['normal', 'words', 'all', 'keep']\n      }],\n      /**\n       * Hyphens\n       * @see https://tailwindcss.com/docs/hyphens\n       */\n      hyphens: [{\n        hyphens: ['none', 'manual', 'auto']\n      }],\n      /**\n       * Content\n       * @see https://tailwindcss.com/docs/content\n       */\n      content: [{\n        content: ['none', isArbitraryValue]\n      }],\n      // Backgrounds\n      /**\n       * Background Attachment\n       * @see https://tailwindcss.com/docs/background-attachment\n       */\n      'bg-attachment': [{\n        bg: ['fixed', 'local', 'scroll']\n      }],\n      /**\n       * Background Clip\n       * @see https://tailwindcss.com/docs/background-clip\n       */\n      'bg-clip': [{\n        'bg-clip': ['border', 'padding', 'content', 'text']\n      }],\n      /**\n       * Background Opacity\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://tailwindcss.com/docs/background-opacity\n       */\n      'bg-opacity': [{\n        'bg-opacity': [opacity]\n      }],\n      /**\n       * Background Origin\n       * @see https://tailwindcss.com/docs/background-origin\n       */\n      'bg-origin': [{\n        'bg-origin': ['border', 'padding', 'content']\n      }],\n      /**\n       * Background Position\n       * @see https://tailwindcss.com/docs/background-position\n       */\n      'bg-position': [{\n        bg: [...getPositions(), isArbitraryPosition]\n      }],\n      /**\n       * Background Repeat\n       * @see https://tailwindcss.com/docs/background-repeat\n       */\n      'bg-repeat': [{\n        bg: ['no-repeat', {\n          repeat: ['', 'x', 'y', 'round', 'space']\n        }]\n      }],\n      /**\n       * Background Size\n       * @see https://tailwindcss.com/docs/background-size\n       */\n      'bg-size': [{\n        bg: ['auto', 'cover', 'contain', isArbitrarySize]\n      }],\n      /**\n       * Background Image\n       * @see https://tailwindcss.com/docs/background-image\n       */\n      'bg-image': [{\n        bg: ['none', {\n          'gradient-to': ['t', 'tr', 'r', 'br', 'b', 'bl', 'l', 'tl']\n        }, isArbitraryImage]\n      }],\n      /**\n       * Background Color\n       * @see https://tailwindcss.com/docs/background-color\n       */\n      'bg-color': [{\n        bg: [colors]\n      }],\n      /**\n       * Gradient Color Stops From Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-from-pos': [{\n        from: [gradientColorStopPositions]\n      }],\n      /**\n       * Gradient Color Stops Via Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-via-pos': [{\n        via: [gradientColorStopPositions]\n      }],\n      /**\n       * Gradient Color Stops To Position\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-to-pos': [{\n        to: [gradientColorStopPositions]\n      }],\n      /**\n       * Gradient Color Stops From\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-from': [{\n        from: [gradientColorStops]\n      }],\n      /**\n       * Gradient Color Stops Via\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-via': [{\n        via: [gradientColorStops]\n      }],\n      /**\n       * Gradient Color Stops To\n       * @see https://tailwindcss.com/docs/gradient-color-stops\n       */\n      'gradient-to': [{\n        to: [gradientColorStops]\n      }],\n      // Borders\n      /**\n       * Border Radius\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      rounded: [{\n        rounded: [borderRadius]\n      }],\n      /**\n       * Border Radius Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-s': [{\n        'rounded-s': [borderRadius]\n      }],\n      /**\n       * Border Radius End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-e': [{\n        'rounded-e': [borderRadius]\n      }],\n      /**\n       * Border Radius Top\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-t': [{\n        'rounded-t': [borderRadius]\n      }],\n      /**\n       * Border Radius Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-r': [{\n        'rounded-r': [borderRadius]\n      }],\n      /**\n       * Border Radius Bottom\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-b': [{\n        'rounded-b': [borderRadius]\n      }],\n      /**\n       * Border Radius Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-l': [{\n        'rounded-l': [borderRadius]\n      }],\n      /**\n       * Border Radius Start Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-ss': [{\n        'rounded-ss': [borderRadius]\n      }],\n      /**\n       * Border Radius Start End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-se': [{\n        'rounded-se': [borderRadius]\n      }],\n      /**\n       * Border Radius End End\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-ee': [{\n        'rounded-ee': [borderRadius]\n      }],\n      /**\n       * Border Radius End Start\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-es': [{\n        'rounded-es': [borderRadius]\n      }],\n      /**\n       * Border Radius Top Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-tl': [{\n        'rounded-tl': [borderRadius]\n      }],\n      /**\n       * Border Radius Top Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-tr': [{\n        'rounded-tr': [borderRadius]\n      }],\n      /**\n       * Border Radius Bottom Right\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-br': [{\n        'rounded-br': [borderRadius]\n      }],\n      /**\n       * Border Radius Bottom Left\n       * @see https://tailwindcss.com/docs/border-radius\n       */\n      'rounded-bl': [{\n        'rounded-bl': [borderRadius]\n      }],\n      /**\n       * Border Width\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w': [{\n        border: [borderWidth]\n      }],\n      /**\n       * Border Width X\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-x': [{\n        'border-x': [borderWidth]\n      }],\n      /**\n       * Border Width Y\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-y': [{\n        'border-y': [borderWidth]\n      }],\n      /**\n       * Border Width Start\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-s': [{\n        'border-s': [borderWidth]\n      }],\n      /**\n       * Border Width End\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-e': [{\n        'border-e': [borderWidth]\n      }],\n      /**\n       * Border Width Top\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-t': [{\n        'border-t': [borderWidth]\n      }],\n      /**\n       * Border Width Right\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-r': [{\n        'border-r': [borderWidth]\n      }],\n      /**\n       * Border Width Bottom\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-b': [{\n        'border-b': [borderWidth]\n      }],\n      /**\n       * Border Width Left\n       * @see https://tailwindcss.com/docs/border-width\n       */\n      'border-w-l': [{\n        'border-l': [borderWidth]\n      }],\n      /**\n       * Border Opacity\n       * @see https://tailwindcss.com/docs/border-opacity\n       */\n      'border-opacity': [{\n        'border-opacity': [opacity]\n      }],\n      /**\n       * Border Style\n       * @see https://tailwindcss.com/docs/border-style\n       */\n      'border-style': [{\n        border: [...getLineStyles(), 'hidden']\n      }],\n      /**\n       * Divide Width X\n       * @see https://tailwindcss.com/docs/divide-width\n       */\n      'divide-x': [{\n        'divide-x': [borderWidth]\n      }],\n      /**\n       * Divide Width X Reverse\n       * @see https://tailwindcss.com/docs/divide-width\n       */\n      'divide-x-reverse': ['divide-x-reverse'],\n      /**\n       * Divide Width Y\n       * @see https://tailwindcss.com/docs/divide-width\n       */\n      'divide-y': [{\n        'divide-y': [borderWidth]\n      }],\n      /**\n       * Divide Width Y Reverse\n       * @see https://tailwindcss.com/docs/divide-width\n       */\n      'divide-y-reverse': ['divide-y-reverse'],\n      /**\n       * Divide Opacity\n       * @see https://tailwindcss.com/docs/divide-opacity\n       */\n      'divide-opacity': [{\n        'divide-opacity': [opacity]\n      }],\n      /**\n       * Divide Style\n       * @see https://tailwindcss.com/docs/divide-style\n       */\n      'divide-style': [{\n        divide: getLineStyles()\n      }],\n      /**\n       * Border Color\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color': [{\n        border: [borderColor]\n      }],\n      /**\n       * Border Color X\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-x': [{\n        'border-x': [borderColor]\n      }],\n      /**\n       * Border Color Y\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-y': [{\n        'border-y': [borderColor]\n      }],\n      /**\n       * Border Color S\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-s': [{\n        'border-s': [borderColor]\n      }],\n      /**\n       * Border Color E\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-e': [{\n        'border-e': [borderColor]\n      }],\n      /**\n       * Border Color Top\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-t': [{\n        'border-t': [borderColor]\n      }],\n      /**\n       * Border Color Right\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-r': [{\n        'border-r': [borderColor]\n      }],\n      /**\n       * Border Color Bottom\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-b': [{\n        'border-b': [borderColor]\n      }],\n      /**\n       * Border Color Left\n       * @see https://tailwindcss.com/docs/border-color\n       */\n      'border-color-l': [{\n        'border-l': [borderColor]\n      }],\n      /**\n       * Divide Color\n       * @see https://tailwindcss.com/docs/divide-color\n       */\n      'divide-color': [{\n        divide: [borderColor]\n      }],\n      /**\n       * Outline Style\n       * @see https://tailwindcss.com/docs/outline-style\n       */\n      'outline-style': [{\n        outline: ['', ...getLineStyles()]\n      }],\n      /**\n       * Outline Offset\n       * @see https://tailwindcss.com/docs/outline-offset\n       */\n      'outline-offset': [{\n        'outline-offset': [isLength, isArbitraryValue]\n      }],\n      /**\n       * Outline Width\n       * @see https://tailwindcss.com/docs/outline-width\n       */\n      'outline-w': [{\n        outline: [isLength, isArbitraryLength]\n      }],\n      /**\n       * Outline Color\n       * @see https://tailwindcss.com/docs/outline-color\n       */\n      'outline-color': [{\n        outline: [colors]\n      }],\n      /**\n       * Ring Width\n       * @see https://tailwindcss.com/docs/ring-width\n       */\n      'ring-w': [{\n        ring: getLengthWithEmptyAndArbitrary()\n      }],\n      /**\n       * Ring Width Inset\n       * @see https://tailwindcss.com/docs/ring-width\n       */\n      'ring-w-inset': ['ring-inset'],\n      /**\n       * Ring Color\n       * @see https://tailwindcss.com/docs/ring-color\n       */\n      'ring-color': [{\n        ring: [colors]\n      }],\n      /**\n       * Ring Opacity\n       * @see https://tailwindcss.com/docs/ring-opacity\n       */\n      'ring-opacity': [{\n        'ring-opacity': [opacity]\n      }],\n      /**\n       * Ring Offset Width\n       * @see https://tailwindcss.com/docs/ring-offset-width\n       */\n      'ring-offset-w': [{\n        'ring-offset': [isLength, isArbitraryLength]\n      }],\n      /**\n       * Ring Offset Color\n       * @see https://tailwindcss.com/docs/ring-offset-color\n       */\n      'ring-offset-color': [{\n        'ring-offset': [colors]\n      }],\n      // Effects\n      /**\n       * Box Shadow\n       * @see https://tailwindcss.com/docs/box-shadow\n       */\n      shadow: [{\n        shadow: ['', 'inner', 'none', isTshirtSize, isArbitraryShadow]\n      }],\n      /**\n       * Box Shadow Color\n       * @see https://tailwindcss.com/docs/box-shadow-color\n       */\n      'shadow-color': [{\n        shadow: [isAny]\n      }],\n      /**\n       * Opacity\n       * @see https://tailwindcss.com/docs/opacity\n       */\n      opacity: [{\n        opacity: [opacity]\n      }],\n      /**\n       * Mix Blend Mode\n       * @see https://tailwindcss.com/docs/mix-blend-mode\n       */\n      'mix-blend': [{\n        'mix-blend': [...getBlendModes(), 'plus-lighter', 'plus-darker']\n      }],\n      /**\n       * Background Blend Mode\n       * @see https://tailwindcss.com/docs/background-blend-mode\n       */\n      'bg-blend': [{\n        'bg-blend': getBlendModes()\n      }],\n      // Filters\n      /**\n       * Filter\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://tailwindcss.com/docs/filter\n       */\n      filter: [{\n        filter: ['', 'none']\n      }],\n      /**\n       * Blur\n       * @see https://tailwindcss.com/docs/blur\n       */\n      blur: [{\n        blur: [blur]\n      }],\n      /**\n       * Brightness\n       * @see https://tailwindcss.com/docs/brightness\n       */\n      brightness: [{\n        brightness: [brightness]\n      }],\n      /**\n       * Contrast\n       * @see https://tailwindcss.com/docs/contrast\n       */\n      contrast: [{\n        contrast: [contrast]\n      }],\n      /**\n       * Drop Shadow\n       * @see https://tailwindcss.com/docs/drop-shadow\n       */\n      'drop-shadow': [{\n        'drop-shadow': ['', 'none', isTshirtSize, isArbitraryValue]\n      }],\n      /**\n       * Grayscale\n       * @see https://tailwindcss.com/docs/grayscale\n       */\n      grayscale: [{\n        grayscale: [grayscale]\n      }],\n      /**\n       * Hue Rotate\n       * @see https://tailwindcss.com/docs/hue-rotate\n       */\n      'hue-rotate': [{\n        'hue-rotate': [hueRotate]\n      }],\n      /**\n       * Invert\n       * @see https://tailwindcss.com/docs/invert\n       */\n      invert: [{\n        invert: [invert]\n      }],\n      /**\n       * Saturate\n       * @see https://tailwindcss.com/docs/saturate\n       */\n      saturate: [{\n        saturate: [saturate]\n      }],\n      /**\n       * Sepia\n       * @see https://tailwindcss.com/docs/sepia\n       */\n      sepia: [{\n        sepia: [sepia]\n      }],\n      /**\n       * Backdrop Filter\n       * @deprecated since Tailwind CSS v3.0.0\n       * @see https://tailwindcss.com/docs/backdrop-filter\n       */\n      'backdrop-filter': [{\n        'backdrop-filter': ['', 'none']\n      }],\n      /**\n       * Backdrop Blur\n       * @see https://tailwindcss.com/docs/backdrop-blur\n       */\n      'backdrop-blur': [{\n        'backdrop-blur': [blur]\n      }],\n      /**\n       * Backdrop Brightness\n       * @see https://tailwindcss.com/docs/backdrop-brightness\n       */\n      'backdrop-brightness': [{\n        'backdrop-brightness': [brightness]\n      }],\n      /**\n       * Backdrop Contrast\n       * @see https://tailwindcss.com/docs/backdrop-contrast\n       */\n      'backdrop-contrast': [{\n        'backdrop-contrast': [contrast]\n      }],\n      /**\n       * Backdrop Grayscale\n       * @see https://tailwindcss.com/docs/backdrop-grayscale\n       */\n      'backdrop-grayscale': [{\n        'backdrop-grayscale': [grayscale]\n      }],\n      /**\n       * Backdrop Hue Rotate\n       * @see https://tailwindcss.com/docs/backdrop-hue-rotate\n       */\n      'backdrop-hue-rotate': [{\n        'backdrop-hue-rotate': [hueRotate]\n      }],\n      /**\n       * Backdrop Invert\n       * @see https://tailwindcss.com/docs/backdrop-invert\n       */\n      'backdrop-invert': [{\n        'backdrop-invert': [invert]\n      }],\n      /**\n       * Backdrop Opacity\n       * @see https://tailwindcss.com/docs/backdrop-opacity\n       */\n      'backdrop-opacity': [{\n        'backdrop-opacity': [opacity]\n      }],\n      /**\n       * Backdrop Saturate\n       * @see https://tailwindcss.com/docs/backdrop-saturate\n       */\n      'backdrop-saturate': [{\n        'backdrop-saturate': [saturate]\n      }],\n      /**\n       * Backdrop Sepia\n       * @see https://tailwindcss.com/docs/backdrop-sepia\n       */\n      'backdrop-sepia': [{\n        'backdrop-sepia': [sepia]\n      }],\n      // Tables\n      /**\n       * Border Collapse\n       * @see https://tailwindcss.com/docs/border-collapse\n       */\n      'border-collapse': [{\n        border: ['collapse', 'separate']\n      }],\n      /**\n       * Border Spacing\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing': [{\n        'border-spacing': [borderSpacing]\n      }],\n      /**\n       * Border Spacing X\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing-x': [{\n        'border-spacing-x': [borderSpacing]\n      }],\n      /**\n       * Border Spacing Y\n       * @see https://tailwindcss.com/docs/border-spacing\n       */\n      'border-spacing-y': [{\n        'border-spacing-y': [borderSpacing]\n      }],\n      /**\n       * Table Layout\n       * @see https://tailwindcss.com/docs/table-layout\n       */\n      'table-layout': [{\n        table: ['auto', 'fixed']\n      }],\n      /**\n       * Caption Side\n       * @see https://tailwindcss.com/docs/caption-side\n       */\n      caption: [{\n        caption: ['top', 'bottom']\n      }],\n      // Transitions and Animation\n      /**\n       * Tranisition Property\n       * @see https://tailwindcss.com/docs/transition-property\n       */\n      transition: [{\n        transition: ['none', 'all', '', 'colors', 'opacity', 'shadow', 'transform', isArbitraryValue]\n      }],\n      /**\n       * Transition Duration\n       * @see https://tailwindcss.com/docs/transition-duration\n       */\n      duration: [{\n        duration: getNumberAndArbitrary()\n      }],\n      /**\n       * Transition Timing Function\n       * @see https://tailwindcss.com/docs/transition-timing-function\n       */\n      ease: [{\n        ease: ['linear', 'in', 'out', 'in-out', isArbitraryValue]\n      }],\n      /**\n       * Transition Delay\n       * @see https://tailwindcss.com/docs/transition-delay\n       */\n      delay: [{\n        delay: getNumberAndArbitrary()\n      }],\n      /**\n       * Animation\n       * @see https://tailwindcss.com/docs/animation\n       */\n      animate: [{\n        animate: ['none', 'spin', 'ping', 'pulse', 'bounce', isArbitraryValue]\n      }],\n      // Transforms\n      /**\n       * Transform\n       * @see https://tailwindcss.com/docs/transform\n       */\n      transform: [{\n        transform: ['', 'gpu', 'none']\n      }],\n      /**\n       * Scale\n       * @see https://tailwindcss.com/docs/scale\n       */\n      scale: [{\n        scale: [scale]\n      }],\n      /**\n       * Scale X\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-x': [{\n        'scale-x': [scale]\n      }],\n      /**\n       * Scale Y\n       * @see https://tailwindcss.com/docs/scale\n       */\n      'scale-y': [{\n        'scale-y': [scale]\n      }],\n      /**\n       * Rotate\n       * @see https://tailwindcss.com/docs/rotate\n       */\n      rotate: [{\n        rotate: [isInteger, isArbitraryValue]\n      }],\n      /**\n       * Translate X\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-x': [{\n        'translate-x': [translate]\n      }],\n      /**\n       * Translate Y\n       * @see https://tailwindcss.com/docs/translate\n       */\n      'translate-y': [{\n        'translate-y': [translate]\n      }],\n      /**\n       * Skew X\n       * @see https://tailwindcss.com/docs/skew\n       */\n      'skew-x': [{\n        'skew-x': [skew]\n      }],\n      /**\n       * Skew Y\n       * @see https://tailwindcss.com/docs/skew\n       */\n      'skew-y': [{\n        'skew-y': [skew]\n      }],\n      /**\n       * Transform Origin\n       * @see https://tailwindcss.com/docs/transform-origin\n       */\n      'transform-origin': [{\n        origin: ['center', 'top', 'top-right', 'right', 'bottom-right', 'bottom', 'bottom-left', 'left', 'top-left', isArbitraryValue]\n      }],\n      // Interactivity\n      /**\n       * Accent Color\n       * @see https://tailwindcss.com/docs/accent-color\n       */\n      accent: [{\n        accent: ['auto', colors]\n      }],\n      /**\n       * Appearance\n       * @see https://tailwindcss.com/docs/appearance\n       */\n      appearance: [{\n        appearance: ['none', 'auto']\n      }],\n      /**\n       * Cursor\n       * @see https://tailwindcss.com/docs/cursor\n       */\n      cursor: [{\n        cursor: ['auto', 'default', 'pointer', 'wait', 'text', 'move', 'help', 'not-allowed', 'none', 'context-menu', 'progress', 'cell', 'crosshair', 'vertical-text', 'alias', 'copy', 'no-drop', 'grab', 'grabbing', 'all-scroll', 'col-resize', 'row-resize', 'n-resize', 'e-resize', 's-resize', 'w-resize', 'ne-resize', 'nw-resize', 'se-resize', 'sw-resize', 'ew-resize', 'ns-resize', 'nesw-resize', 'nwse-resize', 'zoom-in', 'zoom-out', isArbitraryValue]\n      }],\n      /**\n       * Caret Color\n       * @see https://tailwindcss.com/docs/just-in-time-mode#caret-color-utilities\n       */\n      'caret-color': [{\n        caret: [colors]\n      }],\n      /**\n       * Pointer Events\n       * @see https://tailwindcss.com/docs/pointer-events\n       */\n      'pointer-events': [{\n        'pointer-events': ['none', 'auto']\n      }],\n      /**\n       * Resize\n       * @see https://tailwindcss.com/docs/resize\n       */\n      resize: [{\n        resize: ['none', 'y', 'x', '']\n      }],\n      /**\n       * Scroll Behavior\n       * @see https://tailwindcss.com/docs/scroll-behavior\n       */\n      'scroll-behavior': [{\n        scroll: ['auto', 'smooth']\n      }],\n      /**\n       * Scroll Margin\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-m': [{\n        'scroll-m': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin X\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mx': [{\n        'scroll-mx': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Y\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-my': [{\n        'scroll-my': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Start\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-ms': [{\n        'scroll-ms': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin End\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-me': [{\n        'scroll-me': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Top\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mt': [{\n        'scroll-mt': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Right\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mr': [{\n        'scroll-mr': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Bottom\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-mb': [{\n        'scroll-mb': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Margin Left\n       * @see https://tailwindcss.com/docs/scroll-margin\n       */\n      'scroll-ml': [{\n        'scroll-ml': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-p': [{\n        'scroll-p': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding X\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-px': [{\n        'scroll-px': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Y\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-py': [{\n        'scroll-py': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Start\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-ps': [{\n        'scroll-ps': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding End\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pe': [{\n        'scroll-pe': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Top\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pt': [{\n        'scroll-pt': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Right\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pr': [{\n        'scroll-pr': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Bottom\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pb': [{\n        'scroll-pb': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Padding Left\n       * @see https://tailwindcss.com/docs/scroll-padding\n       */\n      'scroll-pl': [{\n        'scroll-pl': getSpacingWithArbitrary()\n      }],\n      /**\n       * Scroll Snap Align\n       * @see https://tailwindcss.com/docs/scroll-snap-align\n       */\n      'snap-align': [{\n        snap: ['start', 'end', 'center', 'align-none']\n      }],\n      /**\n       * Scroll Snap Stop\n       * @see https://tailwindcss.com/docs/scroll-snap-stop\n       */\n      'snap-stop': [{\n        snap: ['normal', 'always']\n      }],\n      /**\n       * Scroll Snap Type\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */\n      'snap-type': [{\n        snap: ['none', 'x', 'y', 'both']\n      }],\n      /**\n       * Scroll Snap Type Strictness\n       * @see https://tailwindcss.com/docs/scroll-snap-type\n       */\n      'snap-strictness': [{\n        snap: ['mandatory', 'proximity']\n      }],\n      /**\n       * Touch Action\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      touch: [{\n        touch: ['auto', 'none', 'manipulation']\n      }],\n      /**\n       * Touch Action X\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-x': [{\n        'touch-pan': ['x', 'left', 'right']\n      }],\n      /**\n       * Touch Action Y\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-y': [{\n        'touch-pan': ['y', 'up', 'down']\n      }],\n      /**\n       * Touch Action Pinch Zoom\n       * @see https://tailwindcss.com/docs/touch-action\n       */\n      'touch-pz': ['touch-pinch-zoom'],\n      /**\n       * User Select\n       * @see https://tailwindcss.com/docs/user-select\n       */\n      select: [{\n        select: ['none', 'text', 'all', 'auto']\n      }],\n      /**\n       * Will Change\n       * @see https://tailwindcss.com/docs/will-change\n       */\n      'will-change': [{\n        'will-change': ['auto', 'scroll', 'contents', 'transform', isArbitraryValue]\n      }],\n      // SVG\n      /**\n       * Fill\n       * @see https://tailwindcss.com/docs/fill\n       */\n      fill: [{\n        fill: [colors, 'none']\n      }],\n      /**\n       * Stroke Width\n       * @see https://tailwindcss.com/docs/stroke-width\n       */\n      'stroke-w': [{\n        stroke: [isLength, isArbitraryLength, isArbitraryNumber]\n      }],\n      /**\n       * Stroke\n       * @see https://tailwindcss.com/docs/stroke\n       */\n      stroke: [{\n        stroke: [colors, 'none']\n      }],\n      // Accessibility\n      /**\n       * Screen Readers\n       * @see https://tailwindcss.com/docs/screen-readers\n       */\n      sr: ['sr-only', 'not-sr-only'],\n      /**\n       * Forced Color Adjust\n       * @see https://tailwindcss.com/docs/forced-color-adjust\n       */\n      'forced-color-adjust': [{\n        'forced-color-adjust': ['auto', 'none']\n      }]\n    },\n    conflictingClassGroups: {\n      overflow: ['overflow-x', 'overflow-y'],\n      overscroll: ['overscroll-x', 'overscroll-y'],\n      inset: ['inset-x', 'inset-y', 'start', 'end', 'top', 'right', 'bottom', 'left'],\n      'inset-x': ['right', 'left'],\n      'inset-y': ['top', 'bottom'],\n      flex: ['basis', 'grow', 'shrink'],\n      gap: ['gap-x', 'gap-y'],\n      p: ['px', 'py', 'ps', 'pe', 'pt', 'pr', 'pb', 'pl'],\n      px: ['pr', 'pl'],\n      py: ['pt', 'pb'],\n      m: ['mx', 'my', 'ms', 'me', 'mt', 'mr', 'mb', 'ml'],\n      mx: ['mr', 'ml'],\n      my: ['mt', 'mb'],\n      size: ['w', 'h'],\n      'font-size': ['leading'],\n      'fvn-normal': ['fvn-ordinal', 'fvn-slashed-zero', 'fvn-figure', 'fvn-spacing', 'fvn-fraction'],\n      'fvn-ordinal': ['fvn-normal'],\n      'fvn-slashed-zero': ['fvn-normal'],\n      'fvn-figure': ['fvn-normal'],\n      'fvn-spacing': ['fvn-normal'],\n      'fvn-fraction': ['fvn-normal'],\n      'line-clamp': ['display', 'overflow'],\n      rounded: ['rounded-s', 'rounded-e', 'rounded-t', 'rounded-r', 'rounded-b', 'rounded-l', 'rounded-ss', 'rounded-se', 'rounded-ee', 'rounded-es', 'rounded-tl', 'rounded-tr', 'rounded-br', 'rounded-bl'],\n      'rounded-s': ['rounded-ss', 'rounded-es'],\n      'rounded-e': ['rounded-se', 'rounded-ee'],\n      'rounded-t': ['rounded-tl', 'rounded-tr'],\n      'rounded-r': ['rounded-tr', 'rounded-br'],\n      'rounded-b': ['rounded-br', 'rounded-bl'],\n      'rounded-l': ['rounded-tl', 'rounded-bl'],\n      'border-spacing': ['border-spacing-x', 'border-spacing-y'],\n      'border-w': ['border-w-s', 'border-w-e', 'border-w-t', 'border-w-r', 'border-w-b', 'border-w-l'],\n      'border-w-x': ['border-w-r', 'border-w-l'],\n      'border-w-y': ['border-w-t', 'border-w-b'],\n      'border-color': ['border-color-s', 'border-color-e', 'border-color-t', 'border-color-r', 'border-color-b', 'border-color-l'],\n      'border-color-x': ['border-color-r', 'border-color-l'],\n      'border-color-y': ['border-color-t', 'border-color-b'],\n      'scroll-m': ['scroll-mx', 'scroll-my', 'scroll-ms', 'scroll-me', 'scroll-mt', 'scroll-mr', 'scroll-mb', 'scroll-ml'],\n      'scroll-mx': ['scroll-mr', 'scroll-ml'],\n      'scroll-my': ['scroll-mt', 'scroll-mb'],\n      'scroll-p': ['scroll-px', 'scroll-py', 'scroll-ps', 'scroll-pe', 'scroll-pt', 'scroll-pr', 'scroll-pb', 'scroll-pl'],\n      'scroll-px': ['scroll-pr', 'scroll-pl'],\n      'scroll-py': ['scroll-pt', 'scroll-pb'],\n      touch: ['touch-x', 'touch-y', 'touch-pz'],\n      'touch-x': ['touch'],\n      'touch-y': ['touch'],\n      'touch-pz': ['touch']\n    },\n    conflictingClassGroupModifiers: {\n      'font-size': ['leading']\n    }\n  };\n};\n\n/**\n * @param baseConfig Config where other config will be merged into. This object will be mutated.\n * @param configExtension Partial config to merge into the `baseConfig`.\n */\nconst mergeConfigs = (baseConfig, {\n  cacheSize,\n  prefix,\n  separator,\n  experimentalParseClassName,\n  extend = {},\n  override = {}\n}) => {\n  overrideProperty(baseConfig, 'cacheSize', cacheSize);\n  overrideProperty(baseConfig, 'prefix', prefix);\n  overrideProperty(baseConfig, 'separator', separator);\n  overrideProperty(baseConfig, 'experimentalParseClassName', experimentalParseClassName);\n  for (const configKey in override) {\n    overrideConfigProperties(baseConfig[configKey], override[configKey]);\n  }\n  for (const key in extend) {\n    mergeConfigProperties(baseConfig[key], extend[key]);\n  }\n  return baseConfig;\n};\nconst overrideProperty = (baseObject, overrideKey, overrideValue) => {\n  if (overrideValue !== undefined) {\n    baseObject[overrideKey] = overrideValue;\n  }\n};\nconst overrideConfigProperties = (baseObject, overrideObject) => {\n  if (overrideObject) {\n    for (const key in overrideObject) {\n      overrideProperty(baseObject, key, overrideObject[key]);\n    }\n  }\n};\nconst mergeConfigProperties = (baseObject, mergeObject) => {\n  if (mergeObject) {\n    for (const key in mergeObject) {\n      const mergeValue = mergeObject[key];\n      if (mergeValue !== undefined) {\n        baseObject[key] = (baseObject[key] || []).concat(mergeValue);\n      }\n    }\n  }\n};\nconst extendTailwindMerge = (configExtension, ...createConfig) => typeof configExtension === 'function' ? createTailwindMerge(getDefaultConfig, configExtension, ...createConfig) : createTailwindMerge(() => mergeConfigs(getDefaultConfig(), configExtension), ...createConfig);\nconst twMerge = /*#__PURE__*/createTailwindMerge(getDefaultConfig);\n\n//# sourceMappingURL=bundle-mjs.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5Cmyaitts%5C%5Cfrontend%5C%5Capp%5C%5Ctest-auth%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);